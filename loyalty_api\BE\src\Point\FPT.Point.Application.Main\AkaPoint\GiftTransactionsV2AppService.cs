﻿using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Linq.Extensions;
using Abp.Localization;
using Abp.Runtime.Session;
using Abp.Runtime.Validation;
using Abp.UI;
using FPT.Point.Application.Main.Utils;
using FPT.Point.Application.Shared.AkaPoint.Dtos;
using FPT.Point.Application.Shared.AkaPoint.Dtos.ThirdParty.ThirdPartyVendor;
using FPT.Point.Application.Shared.AkaPoint.ThirdParty.ThirdPartyVendor;
using FPT.Point.Core.AkaPoint;
using FPT.Point.Core.Authorization;
using FPT.Point.Core.Helper;
using FPT.Point.Core.Shared;
using FPT.SaaS.AkaPoint.Dtos;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;
using System.Linq.Dynamic.Core;
using FPT.Point.Application;
using FPT.Point.Application.Shared.AkaPoint.Dtos.GiftTransaction;
using FPT.Point.Dto;
using System.Threading;

namespace FPT.SaaS.AkaPoint
{
    public partial class GiftTransactionsAppService
    {
        #region
        /*
         * API NHƯ CŨ NGOẠI TRỪ THÊM GiftGroupCode để xác định chỉ đổi được quà thuộc group xác định.
         * Khởi nguồn từ việc hỗ trợ MasterCard trên CardZone của NEO
        */
        public async Task<VerifyAndCreateRedeemOrderWithGiftGroupOutput> CreateRedeemTxWithGroupCode(RequestCreateRedeemTransactionWithGiftGroup input)
        {
            //  MAKE sure là input.TransactionCode phải có giá trị và tìm được bản ghi CreateGiftRedeemTransaction với code như thế 
            if (string.IsNullOrWhiteSpace(input.TransactionCode))
            {
                throw new UserFriendlyException(10002,
                    langMutil("TransactionCodeIsRequired"));
            }

            var temp = await _createGiftTransactionRepository.FirstOrDefaultAsync(x =>
                x.IsDeleted == false && x.TransactionCode == input.TransactionCode.Trim() && x.Status == "Pending");
            if (temp == null)
            {
                throw new UserFriendlyException(10002,
                    langMutil("TransactionCodeDoesnotExist"));
            }
            // Check sự liên hệ giữa Gift và Group, rằng gift thuộc group này
            Logger.Info($" >> VerifyAndCreateRedeemOrderWithGiftGroup >> {input.GiftGroupCode} - {input.GiftCode} - {input.MemberCode}");
            if (string.IsNullOrEmpty(input.GiftGroupCode))
            {
                throw new UserFriendlyException(10000,
                    langMutil("GiftGroupIsRequired"));
            }
            var channelCode = input.RedeemSource;
            if (new List<string> { "247" }.Contains(input.RedeemSource))
            {
                channelCode = "vpbank";
            }
            var channel = await GetCachedChannel(channelCode);
            input.GiftGroupCode = input.GiftGroupCode.Trim();
            // 2 là check group có allow channel
            var giftGroup = await GetCachedGiftGroup(input.GiftGroupCode);
            if (giftGroup.ChannelId != channel.Id)
            {
                throw new UserFriendlyException(10001,
                    langMutil("GiftGroupIsNotAllowed"));
            }
            Logger.Info($" >> VerifyAndCreateRedeemOrderWithGiftGroup >> GiftGroup and Channel are match! {input.GiftGroupCode} vs {channelCode}");
            // 3 Kiểm tra Quà nằm trong group
            var checkGiftAndGroup = await _giftGroupDetailRepo.GetAll().AnyAsync(x =>
                x.IsDeleted == false && x.Status == "A" && x.GiftCode == input.GiftCode &&
                x.GiftGroupCode == giftGroup.Code);
            if (!checkGiftAndGroup)
            {
                throw new UserFriendlyException(10002,
                    langMutil("GiftAndGiftGroupNotMatch"));
            }
            // OK now call the old VerifyAndCreateRedeemOrder()
            Logger.Info($" >> VerifyAndCreateRedeemOrderWithGiftGroup >> Now call CreateRedeemTransaction ...");

            var request = new RequestCreateRedeemTransaction()
            {
                Date = input.Date,
                Description = input.Description,
                GiftCode = input.GiftCode,
                MemberCode = input.MemberCode,
                Quantity = input.Quantity,
                RedeemSource = string.IsNullOrWhiteSpace(input.RedeemSource) ? "VPB" : input.RedeemSource,
                TotalAmount = input.TotalAmount,
                TransactionCode = input.TransactionCode,
                MerchantIdRedeem = input.MerchantIdRedeem,
                CardCode = input.CardCode,
                CardCodeTx = input.CardCodeTx
            };
            var redeemResponse = await CreateRedeemTransaction(request);
            this.Logger.Info(" >> Redeem result >> " + JsonConvert.SerializeObject(redeemResponse));
            if (redeemResponse.SuccessedRedeem)
            {
                return new VerifyAndCreateRedeemOrderWithGiftGroupOutput()
                {
                    TransactionCode = redeemResponse.Items[0].Code, EgiftCode = redeemResponse.Items[0].EGift.Code, IsSuccess = true,
                    Exception = "", ExpiredDate = redeemResponse.Items[0].EGift.ExpiredDate, Timeout = redeemResponse.Timeout
                };
            }
            return new VerifyAndCreateRedeemOrderWithGiftGroupOutput()
            {
                TransactionCode = "", EgiftCode = "", IsSuccess = false,
                Exception = redeemResponse.Exception, ExpiredDate = null,
                Timeout = redeemResponse.Timeout

            };
        }

        public async Task<VerifyAndCreateRedeemMultiOrderWithGiftGroupOutput> CreateRedeemMultiTxWithGroupCode(RequestCreateRedeemTransactionWithGiftGroup input)
        {
            // SETUP FOR INTENTIONAL ERROR
            if ("GiftInfor_20240611101128179_2008".Equals(input.GiftCode))
            {
                throw new UserFriendlyException((int)ErrorCodeMessageGiftTransactions.GiftDisabled, langMutil("GiftDisabled"));
            }
            // END
            //  MAKE sure là input.TransactionCode phải có giá trị và tìm được bản ghi CreateGiftRedeemTransaction với code như thế 
            if (string.IsNullOrWhiteSpace(input.TransactionCode))
            {
                throw new UserFriendlyException(10002,
                    langMutil("TransactionCodeIsRequired"));
            }

            var temp = await _createGiftTransactionRepository.FirstOrDefaultAsync(x =>
                x.IsDeleted == false && x.TransactionCode == input.TransactionCode.Trim() && x.Status == "Pending");
            if (temp == null)
            {
                throw new UserFriendlyException(10002,
                    langMutil("TransactionCodeDoesnotExist"));
            }
            // Check sự liên hệ giữa Gift và Group, rằng gift thuộc group này
            Logger.Info($" >> VerifyAndCreateRedeemOrderWithGiftGroup >> {input.GiftGroupCode} - {input.GiftCode} - {input.MemberCode}");
            if (string.IsNullOrEmpty(input.GiftGroupCode))
            {
                throw new UserFriendlyException(10000,
                    langMutil("GiftGroupIsRequired"));
            }
            var channelCode = input.RedeemSource;
            if (new List<string> { "247" }.Contains(input.RedeemSource))
            {
                channelCode = "vpbank";
            }
            var channel = await GetCachedChannel(channelCode);
            input.GiftGroupCode = input.GiftGroupCode.Trim();
            // 2 là check group có allow channel
            var giftGroup = await GetCachedGiftGroup(input.GiftGroupCode);
            if (giftGroup.ChannelId != channel.Id)
            {
                throw new UserFriendlyException(10001,
                    langMutil("GiftGroupIsNotAllowed"));
            }
            Logger.Info($" >> VerifyAndCreateRedeemOrderWithGiftGroup >> GiftGroup and Channel are match! {input.GiftGroupCode} vs {channelCode}");
            // 3 Kiểm tra Quà nằm trong group
            var checkGiftAndGroup = await _giftGroupDetailRepo.GetAll().AnyAsync(x =>
                x.IsDeleted == false && x.Status == "A" && x.GiftCode == input.GiftCode &&
                x.GiftGroupCode == giftGroup.Code);
            if (!checkGiftAndGroup)
            {
                throw new UserFriendlyException(10002,
                    langMutil("GiftAndGiftGroupNotMatch"));
            }
            // OK now call the old VerifyAndCreateRedeemOrder()
            Logger.Info($" >> VerifyAndCreateRedeemOrderWithGiftGroup >> Now call CreateRedeemTransaction ...");

            var request = new RequestCreateRedeemTransaction()
            {
                Date = input.Date,
                Description = input.Description,
                GiftCode = input.GiftCode,
                MemberCode = input.MemberCode,
                Quantity = input.Quantity,
                RedeemSource = string.IsNullOrWhiteSpace(input.RedeemSource) ? "VPB" : input.RedeemSource,
                TotalAmount = input.TotalAmount,
                TransactionCode = input.TransactionCode,
                MerchantIdRedeem = input.MerchantIdRedeem,
                CardCode = input.CardCode,
                CardCodeTx = input.CardCodeTx
            };
            var redeemResponse = await CreateRedeemTransaction(request);
            this.Logger.Info(" >> Redeem result >> " + JsonConvert.SerializeObject(redeemResponse));
            if (redeemResponse.SuccessedRedeem)
            {
                // SETUP TO TEST TIMEOUT AND EXCEPTION CASE FOR MASTERCARD INTEGRATION
                if ("GiftInfor_20240110073301021_00100".Equals(input.GiftCode))
                {
                    Logger.Info(" >> GiftInfor_20240110073301021_00100 >> SLEEP 90s");
                    Thread.Sleep(90000);
                }
                // END
                return new VerifyAndCreateRedeemMultiOrderWithGiftGroupOutput()
                {
                    IsSuccess = true,
                    Exception = "",
                    Timeout = redeemResponse.Timeout,
                    items = redeemResponse.Items.Select(x => new ListEGiftInforOutput() { 
                        EgiftCode = x.EGift.Code,
                        ExpiredDate = x.EGift.ExpiredDate,
                        TransactionCode = x.Code
                    }).ToList()
                };
            }
            return new VerifyAndCreateRedeemMultiOrderWithGiftGroupOutput()
            {
                IsSuccess = false,
                Exception = redeemResponse.Exception,
                Timeout = redeemResponse.Timeout
            };
        }

        public async Task<PagedResultDto<GetGiftRedeemTransWithGiftGroupOutput>> GetGiftRedeemTransWithGiftGroup(GetGiftRedeemTransWithGiftGroupInput input)
        {
            Logger.Info(" >> GetGiftRedeemTransWithGiftGroup " + JsonConvert.SerializeObject(input));

            long memoryBeforeFalse = GC.GetTotalMemory(false);
            Logger.Info($"GetGiftRedeemTransWithGiftGroup___1 (without GC): {memoryBeforeFalse / (1024.0 * 1024.0)} MB");

            var listGiftCode = new List<string>();
            var listAllGiftOfAllGroup = new List<ShortGiftDtoForCardzone>();
            if (!string.IsNullOrEmpty(input.RedeemSource) && input.RedeemSource.ToLower() != "mastercard")
            {
                // Validate GiftGroupCode is valid and can be accessed by the redeemsource
                if (string.IsNullOrEmpty(input.GiftGroupCode))
                {
                    throw new UserFriendlyException(10000,
                        langMutil("GiftGroupIsRequired"));
                }

                var listGroupCodes = input.GiftGroupCode.Trim().Split(";");
                var channelCode = input.RedeemSource;
                if (new List<string> { "247" }.Contains(input.RedeemSource))
                {
                    channelCode = "vpbank";
                }
                var channel = await GetCachedChannel(channelCode);
                // 2 là check group có allow channel
                foreach (var groupCode in listGroupCodes)
                {
                    var giftGroup = await GetCachedGiftGroup(groupCode);
                    if (giftGroup.ChannelId != channel.Id)
                    {
                        throw new UserFriendlyException(10001,
                            langMutil("GiftGroupIsNotAllowed"));
                    }
                }
                //Get list gift of group, from cache or db
                foreach (var groupCode in listGroupCodes)
                {
                    var temp = await GetListGiftOfGroup(groupCode, input.Lang);
                    listAllGiftOfAllGroup.AddRange(temp);
                }
                listGiftCode = listAllGiftOfAllGroup.Select(x => x.Code).Distinct().ToList();
            }
            else
            {
                var temp = await GetListGift(input.MemberCode, input.Lang);
                listAllGiftOfAllGroup.AddRange(temp);
                listGiftCode = listAllGiftOfAllGroup.Select(x => x.Code).Distinct().ToList();
            }

            // Get list giftRedeemTx of user
            var query = _giftTransactionRepository.GetAll()
                .Include(x => x.Gift)
                .Where(x => listGiftCode.Contains(x.GiftCode))
                .Where(x => x.IsDeleted == false && x.BuyerCode == input.MemberCode && x.RedeemSource == input.RedeemSource)
                .WhereIf(input.FromDate.HasValue, x => x.CreationTime >= input.FromDate.Value)
                .WhereIf(input.ToDate.HasValue, x => x.CreationTime <= input.ToDate.Value);
            var count = await query.CountAsync();
            var listTx = await query.OrderByDescending(x => x.Id)
            .PageBy(input)
            .AsNoTracking().ToListAsync();
            var ret = new PagedResultDto<GetGiftRedeemTransWithGiftGroupOutput>()
            {
                    TotalCount = count, Items = listTx.Select(x => new GetGiftRedeemTransWithGiftGroupOutput()
                {
                        Amount = x.Coin ?? 0, TransactionCode = x.TransactionCode, GiftCode = x.GiftCode, GiftName = x.GiftName,
                        GiftCondition = listAllGiftOfAllGroup.FirstOrDefault(y => y.Code == x.GiftCode)?.Condition ?? "",
                        GiftIntroduce = listAllGiftOfAllGroup.FirstOrDefault(y => y.Code == x.GiftCode)?.Introduce ?? "",
                        GiftUsageAddress = listAllGiftOfAllGroup.FirstOrDefault(y => y.Code == x.GiftCode)?.UseAddress ?? "",
                        VendorName = listAllGiftOfAllGroup.FirstOrDefault(y => y.Code == x.GiftCode)?.VendorName ?? "",
                        VendorLogo = listAllGiftOfAllGroup.FirstOrDefault(y => y.Code == x.GiftCode)?.VendorIcon ?? "",
                        BrandName = listAllGiftOfAllGroup.FirstOrDefault(y => y.Code == x.GiftCode)?.BrandName ?? "",
                        BrandLogo = listAllGiftOfAllGroup.FirstOrDefault(y => y.Code == x.GiftCode)?.BrandLogo ?? "",
                        GiftDescription = listAllGiftOfAllGroup.FirstOrDefault(y => y.Code == x.GiftCode)?.Description ?? "",
                        GiftPhoto = listAllGiftOfAllGroup.FirstOrDefault(y => y.Code == x.GiftCode)?.GiftPhoto ?? "",
                        CreationTime = x.CreationTime, TransactionDescription = x.Description,
                        EGiftCode = x.EGiftCode, EGiftExpiredDate = x.EGiftExpiredDate, IsEncrypted = x.IsCodeEncrypyted,
                    Status = x.Status,
                    GiftType = x.Gift?.GiftType,
                    EGiftStatus = x.EGiftStatus
                }).ToList()
            };
            foreach (var tx in ret.Items)
            {
                if (tx.IsEncrypted)
                {
                    try
                    {
                        tx.EGiftCode = await _encryptionUtil.Decrypt(tx.EGiftCode);
                    }
                    catch (Exception e)
                    {
                        Logger.Error("Error Decrypt >> " + tx.TransactionCode);
                    }
                }
            }
            memoryBeforeFalse = GC.GetTotalMemory(false);
            Logger.Info($"GetGiftRedeemTransWithGiftGroup___2 (without GC): {memoryBeforeFalse / (1024.0 * 1024.0)} MB");
            return ret;
        }
        protected async Task<List<ShortGiftDtoForCardzone>> GetListGiftOfGroup(string groupCode, string language)
        {
            var tenantId = AbpSession.TenantId ?? 0;
            if (string.IsNullOrEmpty(groupCode))
            {
                throw new UserFriendlyException("GiftGroupCodeIsRequired");
            }
            // 1. Kiểm tra xem trong cache có ko, có thì lôi ra.
            var cacheKey = "LISTGIFT_OF_GROUP_CACHED_" + groupCode + "_" + language;
            var cachedString = await _cache.GetStringAsync(cacheKey);
            if (!string.IsNullOrEmpty(cachedString))
            {
                // Parse and return if ok, if exception then remove key and retrieve it from DB
                try
                {
                    var parsedObj = JsonConvert.DeserializeObject<List<ShortGiftDtoForCardzone>>(cachedString);
                    return parsedObj;
                }
                catch (Exception e)
                {
                    await _cache.RemoveAsync(cacheKey);
                }
            }
            // Read from DB, set to cache and then return value
            var listGiftCodes = await _giftGroupDetailRepo.GetAll().Where(x => x.IsDeleted == false && x.GiftGroupCode == groupCode && x.Status == "A").AsNoTracking()
                .Select(x => x.GiftCode).ToListAsync();
            var listGift = await _giftInforRepository.GetAll()
                .Where(x => x.IsDeleted == false && listGiftCodes.Contains(x.Code))
                .AsNoTracking()
                .Select(x => new ShortGiftDtoForCardzone()
                {
                    Name = x.Name,
                    Code = x.Code,
                    RequiredCoin = x.RequiredCoin,
                    Condition = x.Condition,
                    Introduce = x.Introduce,/* UseAddress = x.OfficeAddress,*/
                    VendorId = x.VendorId ?? 0,
                    VendorName = x.Vendor,
                    BrandId = x.BrandId,
                    Description = x.Description,
                    Id = x.Id
                }).AsNoTracking().ToListAsync();
            var vendorIdList = listGift.Select(x => x.VendorId).Distinct().ToList();
            var brandIdList = listGift.Select(x => x.BrandId).Distinct().ToList();
            var listVendorObj = await _thirdPartyGiftVendorRepository.GetAll().Where(x => x.IsDeleted == false && vendorIdList.Contains(x.Id))
                .AsNoTracking().ToListAsync();
            var listBrandObj = await _brandInforRepository.GetAll().Where(x => x.IsDeleted == false && brandIdList.Contains(x.Id))
                .AsNoTracking().ToListAsync();
            var imageLinks = await _imageLinkRepository.GetAll()
                .Where(x => x.IsDeleted == false && x.TenantId == tenantId && x.Type == "Gift" && listGiftCodes.Contains(x.Code))
                .AsNoTracking().ToListAsync();

            // Lấy setting GUA MaxResultCount;
            var gUAMaxResultCount = await _giftUsageAddressAppService.GetUsageAddressMaxResultCount();
            Logger.Info($"GetListGiftOfGroup___gUAMaxResultCount:{gUAMaxResultCount}");

            foreach (var gift in listGift)
            {
                if (string.IsNullOrEmpty(gift.UseAddress))
                {
                    string officeUsageAcc = "";
                    var listUsageAddress = await _giftUsageAddressRepository.GetAll().Where(x => x.Status == StatusConst.Active && x.GiftCode == gift.Code && x.Language == language)
                                                .Take(gUAMaxResultCount).AsNoTracking().ToListAsync();
                    if (listUsageAddress != null && listUsageAddress.Count > 0)
                    {
                        officeUsageAcc = string.Format("<br/><p style=\"font-size: 16px; font-weight: bold\">{0}: </p>", (language == "en") ? "Usage address" : "Địa điểm sử dụng");
                        foreach (var x in listUsageAddress)
                        {
                            officeUsageAcc += $"<p style=\"margin: 10px 0px;\">- {x.Name}, {x.Address}</p>";
                        }
                    }

                    gift.UseAddress = officeUsageAcc;
                }

                var vendor = listVendorObj.FirstOrDefault(x => x.Id == gift.VendorId);
                if (vendor != null)
                {
                    gift.VendorName = vendor.VendorName;
                    gift.VendorIcon = vendor.Logo;
                }
                var brand = listBrandObj.FirstOrDefault(x => x.Id == gift.BrandId);
                if (brand != null)
                {
                    gift.BrandName = brand.Name;
                    gift.BrandLogo = brand.LinkLogo;
                }

                var foundImg = imageLinks.Where(x => x.Code == gift.Code).Select(x => x.Link).FirstOrDefault() ?? "";
                gift.GiftPhoto = foundImg;

                if (!string.IsNullOrEmpty(language) && language != "vi")
                {
                    var giftMultiLanguage = await _giftMultiLanguageAppService.GetByGiftIdAndLanguage(gift.Id, language);
                    if (giftMultiLanguage != null)
                    {
                        gift.Name = giftMultiLanguage.GiftName;
                        gift.Condition = giftMultiLanguage.Condition;
                        gift.Description = giftMultiLanguage.Description;
                        gift.Introduce = giftMultiLanguage.Introduce;
                        if (giftMultiLanguage.ImageLinks != null && giftMultiLanguage.ImageLinks.Any())
                        {
                            gift.GiftPhoto = giftMultiLanguage.ImageLinks.FirstOrDefault()?.Link;
                        }
                    }
                }
                if (!string.IsNullOrEmpty(gift.UseAddress) && gift.UseAddress.Contains("**"))
                {
                    gift.UseAddress = gift.UseAddress.Replace("**", "");
                }
            }


            await _cache.SetStringAsync(cacheKey, JsonConvert.SerializeObject(listGift),
                new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(30)));
            return listGift;
        }

        protected async Task<List<ShortGiftDtoForCardzone>> GetListGift(string memberCode, string language)
        {
            var tenantId = AbpSession.TenantId ?? 0;
            // Read from DB, set to cache and then return value
            var listGiftCodes = await _giftTransactionRepository.GetAll().Where(x => x.IsDeleted == false && x.TenantId == tenantId && x.OwnerCode == memberCode).AsNoTracking()
                .Select(x => x.GiftCode).Distinct().ToListAsync();
            var listGift = await _giftInforRepository.GetAll()
                .Where(x => x.IsDeleted == false && listGiftCodes.Contains(x.Code))
                .Select(x => new ShortGiftDtoForCardzone()
                {
                    Name = x.Name,
                    Code = x.Code,
                    RequiredCoin = x.RequiredCoin,
                    Condition = x.Condition,
                    Introduce = x.Introduce,
                    //UseAddress = x.OfficeAddress,
                    VendorId = x.VendorId ?? 0,
                    VendorName = x.Vendor,
                    BrandId = x.BrandId,
                    Description = x.Description,
                    Id = x.Id
                }).AsNoTracking().ToListAsync();
            var vendorIdList = listGift.Select(x => x.VendorId).Distinct().ToList();
            var brandIdList = listGift.Select(x => x.BrandId).Distinct().ToList();
            var listVendorObj = await _thirdPartyGiftVendorRepository.GetAll().Where(x => x.IsDeleted == false && vendorIdList.Contains(x.Id))
                .AsNoTracking().ToListAsync();
            var listBrandObj = await _brandInforRepository.GetAll().Where(x => x.IsDeleted == false && brandIdList.Contains(x.Id))
                .AsNoTracking().ToListAsync();
            var imageLinks = await _imageLinkRepository.GetAll()
                .Where(x => x.IsDeleted == false && x.TenantId == tenantId && x.Type == "Gift" && listGiftCodes.Contains(x.Code))
                .AsNoTracking().ToListAsync();

            // Lấy setting GUA MaxResultCount;
            var gUAMaxResultCount = await _giftUsageAddressAppService.GetUsageAddressMaxResultCount();
            Logger.Info($"GetListGift___gUAMaxResultCount:{gUAMaxResultCount}");

            foreach (var gift in listGift)
            {
                if (string.IsNullOrEmpty(gift.UseAddress))
                {
                    string officeUsageAcc = "";
                    var listUsageAddress = await _giftUsageAddressRepository.GetAll().Where(x => x.Status == StatusConst.Active && x.GiftCode == gift.Code && x.Language == language)
                                                .Take(gUAMaxResultCount).AsNoTracking().ToListAsync();
                    if (listUsageAddress != null && listUsageAddress.Count > 0)
                    {
                        officeUsageAcc = string.Format("<br/><p style=\"font-size: 16px; font-weight: bold\">{0}: </p>", (language == "en") ? "Usage address" : "Địa điểm sử dụng");
                        foreach (var x in listUsageAddress)
                        {
                            officeUsageAcc += $"<p style=\"margin: 10px 0px;\">- {x.Name}, {x.Address}</p>";
                        }
                    }

                    gift.UseAddress = officeUsageAcc;
                }

                var vendor = listVendorObj.FirstOrDefault(x => x.Id == gift.VendorId);
                if (vendor != null)
                {
                    gift.VendorName = vendor.VendorName;
                    gift.VendorIcon = vendor.Logo;
                }
                var brand = listBrandObj.FirstOrDefault(x => x.Id == gift.BrandId);
                if (brand != null)
                {
                    gift.BrandName = brand.Name;
                    gift.BrandLogo = brand.LinkLogo;
                }

                var foundImg = imageLinks.Where(x => x.Code == gift.Code).Select(x => x.Link).FirstOrDefault() ?? "";
                gift.GiftPhoto = foundImg;

                if (!string.IsNullOrEmpty(language) && language != "vi")
                {
                    var giftMultiLanguage = await _giftMultiLanguageAppService.GetByGiftIdAndLanguage(gift.Id, language);
                    if (giftMultiLanguage != null)
                    {
                        gift.Name = giftMultiLanguage.GiftName;
                        gift.Condition = giftMultiLanguage.Condition;
                        gift.Description = giftMultiLanguage.Description;
                        gift.Introduce = giftMultiLanguage.Introduce;
                        if (giftMultiLanguage.ImageLinks != null && giftMultiLanguage.ImageLinks.Any())
                        {
                            gift.GiftPhoto = giftMultiLanguage.ImageLinks.FirstOrDefault()?.Link;
                        }
                    }
                }
                if (!string.IsNullOrEmpty(gift.UseAddress) && gift.UseAddress.Contains("**"))
                {
                    gift.UseAddress = gift.UseAddress.Replace("**", "");
                }
            }
            return listGift;
        }

        protected async Task<GiftGroupDto> GetCachedGiftGroup(string code)
        {
            if (string.IsNullOrEmpty(code))
            {
                throw new UserFriendlyException("GiftGroupCodeIsRequired");
            }
            // 1. Kiểm tra xem trong cache có ko, có thì lôi ra.
            var cacheKey = "GIFT_GROUP_CACHED_" + code;
            var cachedString = await _cache.GetStringAsync(cacheKey);
            if (!string.IsNullOrEmpty(cachedString))
            {
                // Parse and return if ok, if exception then remove key and retrieve it from DB
                try
                {
                    var parsedObj = JsonConvert.DeserializeObject<GiftGroupDto>(cachedString);
                    return parsedObj;
                }
                catch (Exception e)
                {
                    await _cache.RemoveAsync(cacheKey);
                }
            }
            // Read from DB, set to cache and then return value
            var giftGroupFromDb = await _giftGroupRepo.GetAll()
                .Where(x => x.Code == code && x.Status == "A" && x.IsDeleted == false).AsNoTracking().FirstOrDefaultAsync();
            if (giftGroupFromDb == null)
            {
                throw new UserFriendlyException("InvalidGiftGroupCode");
            }
            var tobeCached = new GiftGroupDto()
            {
                Code = code, Id = giftGroupFromDb.Id, Name = giftGroupFromDb.Name, ChannelId = giftGroupFromDb.ChannelId
            };
            await _cache.SetStringAsync(cacheKey, JsonConvert.SerializeObject(tobeCached),
                new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromHours(1)));
            return tobeCached;
        }
        protected async Task<GiftCategoryChannelDto> GetCachedChannel(string channelCode)
        {
            if (string.IsNullOrEmpty(channelCode))
            {
                throw new UserFriendlyException("ChannelCodeIsRequired");
            }
            // 1. Kiểm tra xem trong cache có ko, có thì lôi ra.
            var cacheKey = "GIFT_VIEW_CHANNEL_" + channelCode;
            var cachedString = await _cache.GetStringAsync(cacheKey);
            if (!string.IsNullOrEmpty(cachedString))
            {
                // Parse and return if ok, if exception then remove key and retrieve it from DB
                try
                {
                    var parsedObj = JsonConvert.DeserializeObject<GiftCategoryChannelDto>(cachedString);
                    return parsedObj;
                }
                catch (Exception e)
                {
                    await _cache.RemoveAsync(cacheKey);
                }
            }
            // Read from DB, set to cache and then return value
            var channelFromDb = await _giftCategoryChannelRepository.GetAll()
                .Where(x => x.Code == channelCode && x.IsDeleted == false).AsNoTracking().FirstOrDefaultAsync();
            if (channelFromDb == null)
            {
                throw new UserFriendlyException("InvalidChannelCode");
            }
            var tobeCached = new GiftCategoryChannelDto()
            {
                Code = channelFromDb.Code, Id = channelFromDb.Id
            };
            await _cache.SetStringAsync(cacheKey, JsonConvert.SerializeObject(tobeCached),
                new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromHours(1)));
            return tobeCached;
        }
        // END Comment
        #endregion
        // Giờ là API get bản ghi CreateGiftRedeemTransaction từ OrderCode của TokenTrans, phục vụ view details tx 
        public async Task<GetSingleCreateGiftRedeemTransactionOutput> GetSingleCreateGiftRedeemTransaction(GetSingleCreateGiftRedeemTransactionInput input)
        {
            Logger.Info(" >> GetSingleCreateGiftRedeemTransaction >> " + JsonConvert.SerializeObject(input));
            var found = await _createGiftTransactionRepository.FirstOrDefaultAsync(x =>
                x.BuyerCode == input.MemberCode && x.TransactionCode == input.TransactionCode  && x.IsDeleted == false);
            Logger.Info(" >> GetSingleCreateGiftRedeemTransaction  >> found? " + (found == null ? "NO" : "YES - Id#" + found.Id.ToString() ));
            if (found == null)
            {
                throw new UserFriendlyException("CreateGiftRedeemTransactionNotFound");
            }
            var response = new GetSingleCreateGiftRedeemTransactionOutput()
            {
                GiftCode = found.GiftCode, GiftName = found.GiftName, GiftId = found.GiftId??0,Description = found.Description
            };
            var giftObj = await _giftInforRepository.FirstOrDefaultAsync(x => x.Id == (found.GiftId ?? 0) && x.IsDeleted == false);
            if (giftObj != null)
            {
                response.VendorName = giftObj.Vendor;
                response.Amount = found.TotalCoin ?? 0;
                var brand = await _brandInforRepository.FirstOrDefaultAsync(x => x.Id == (giftObj.BrandId ?? 0) && x.IsDeleted == false);
                var vendor =
                    await _thirdPartyGiftVendorRepository.FirstOrDefaultAsync(x => x.Id == (giftObj.VendorId ?? 0) && x.IsDeleted == false);
                var category =
                    await _giftCategoryRepository.FirstOrDefaultAsync(x => x.Code == giftObj.FullGiftCategoryCode && x.IsDeleted == false);
                if (brand != null)
                {
                    response.BrandImage = brand.LinkLogo;
                    response.BrandName = brand.Name;
                }

                if (vendor != null)
                {
                    response.VendorName = vendor.VendorName;
                    response.VendorImage = vendor.Logo;
                }

                if (category != null)
                {
                    response.FullCategoryCode = giftObj.FullGiftCategoryCode;
                    response.GiftCategoryName = category.Name;
                }

            }
            return response;
        }
        public async Task<VerifyAndCreateRedeemOrderOutput> VerifyAndCreateRedeemOrder(RequestCreateRedeemTransaction input)
        {
            Logger.Info($"VerifyAndCreateRedeemOrder Input {JsonConvert.SerializeObject(input)}");
            if (input.IsNeedVerifyPaymentType)
            {
                var tenantId = AbpSession.TenantId;
                var checkRedeemSource = await _redeemSourceRepository.GetAll()
                                      .Where(x => x.TenantId == tenantId && x.Status == "A" && x.IsDeleted == false)
                                      .AsNoTracking()
                                      .Where(x => x.Code == input.RedeemSource)
                                      .FirstOrDefaultAsync();
                if (checkRedeemSource == null)
                {
                    Logger.Info(" >> RedeemWithPaymentType >> RedeemSource have not config. >> ");
                    throw new UserFriendlyException((int)ErrorCodeList.RedeemSourceNotAvailable,
                        L("RedeemSourceNotAvailable"));
                }

                var permissions = await _redeemSourcePermissionsRepo.GetAll().Where(x =>
                        x.IsDeleted == false && x.TenantId == tenantId && x.Status == "A"
                        && x.RedeemSource == input.RedeemSource
                        && x.Permission == "RedeemGift"
                        && x.PaymentType == input.PaymentType)
                    .FirstOrDefaultAsync();
                if (permissions == null)
                {
                    Logger.Info(" >> RedeemWithPaymentType >> RedeemSourcePerrmisson haven't. >> ");
                    throw new UserFriendlyException((int)ErrorCodeList.RedeemSourceNotPermissions,
                      L("RedeemSourceNotPermissions"));
                }
                // Update trong trường hợp custom(yêu cầu khoảng time nhất định, trường hợp khác ko yêu cầu time)
                var settings = await _redeemSourceSettingRepo.GetAll()
             .Where(x => !x.IsDeleted && x.TenantId == tenantId && x.Status == "A"
                         && string.Equals(x.RedeemSource, input.RedeemSource, StringComparison.OrdinalIgnoreCase)
                         && x.PaymentType == input.PaymentType)
             .AsNoTracking()
             .ToListAsync();
                Logger.Info(" >> CreateRedeemTransactionHandle >> redeemsourcesetting >> list settings >> " + JsonConvert.SerializeObject(settings));
                if (settings.Count == 0)
                {
                    Logger.Warn($" >> CreateRedeemTransactionHandle >> Không có setup cho việc check hạn mức với {input.PaymentSource}" +
                                                       $" vậy nên không thực hiện được giao dịch.");
                    throw new UserFriendlyException((int)ErrorCodeList.ChannelNotAllowedToRedeemWithThisPaymentType,
                        L("ChannelNotAllowedToRedeemWithThisPaymentType"));
                }
                if (settings != null && settings.Count > 0)
                {
                    // Validate hạn mức ngày - nếu có
                    var dateLimit = settings.Where(x => x.LimitType == "DATE").FirstOrDefault();
                    if (dateLimit != null)
                    {
                        var redeemDateInt = CodeGeneratorUtil.ConvertDateToInt(DateTime.UtcNow);
                        decimal sumAmnt = await _giftRedeemTransactionExtRepo.GetAll()
                                    .Where(x => !x.IsDeleted && x.TenantId == tenantId)
                                    .Where(x => x.PaymentSource == input.RedeemSource && x.PaymentType == input.PaymentType)
                                    .Where(x => x.RedeemDateInt == redeemDateInt)
                                    .SumAsync(x => x.Amount);
                        if (sumAmnt + input.TotalAmount > dateLimit.LimitAmount)
                        {
                            Logger.Warn($" >> RedeemWithPaymentType >> Checking hạn mức type = 'DATE' failed!" +
                                        $" Hạn mức là {dateLimit.LimitAmount}, hiện đã dùng tổng {sumAmnt}...");
                            throw new UserFriendlyException((int)ErrorCodeList.PaymentTypeOfThisChannelExceedLimitDATE,
                                L("PaymentTypeOfThisChannelExceedLimitByDATE"));
                        }
                    }
                    var weekLimit = settings.Where(x => x.LimitType == "WEEK").FirstOrDefault();
                    if (weekLimit != null)
                    {
                        var redeemWeekInt = CodeGeneratorUtil.ConvertDateToWeek(DateTime.UtcNow);
                        decimal sumAmnt = await _giftRedeemTransactionExtRepo.GetAll().Where(x =>
                                !x.IsDeleted && x.TenantId == tenantId && x.RedeemWeekInt == redeemWeekInt && x.PaymentSource == input.RedeemSource)
                                .Where(x => x.PaymentType == input.PaymentType)
                            .SumAsync(x => x.Amount);
                        if (sumAmnt + input.TotalAmount > weekLimit.LimitAmount)
                        {
                            Logger.Warn($" >> RedeemWithPaymentType >> Checking hạn mức type = 'WEEK' failed!" +
                                        $" Hạn mức là {weekLimit.LimitAmount}, hiện đã dùng tổng {sumAmnt}...");
                            throw new UserFriendlyException((int)ErrorCodeList.PaymentTypeOfThisChannelExceedLimitWEEK,
                                L("PaymentTypeOfThisChannelExceedLimitByWEEK"));
                        }
                    }
                    var monthLimit = settings.Where(x => x.LimitType == "MONTH").FirstOrDefault();
                    if (monthLimit != null)
                    {
                        var redeemMonthInt = CodeGeneratorUtil.ConvertDateToMonth(DateTime.UtcNow);
                        decimal sumAmnt = await _giftRedeemTransactionExtRepo.GetAll()
                                        .Where(x => !x.IsDeleted && x.TenantId == tenantId)
                                        .Where(x => x.RedeemMonthInt == redeemMonthInt && x.PaymentSource == input.RedeemSource)
                                        .Where(x => x.PaymentType == input.PaymentType)
                                        .SumAsync(x => x.Amount);
                        if (sumAmnt + input.TotalAmount > monthLimit.LimitAmount)
                        {
                            Logger.Warn($" >> RedeemWithPaymentType >> Checking hạn mức type = 'MONTH' failed!" +
                                        $" Hạn mức là {monthLimit.LimitAmount}, hiện đã dùng tổng {sumAmnt}...");
                            throw new UserFriendlyException((int)ErrorCodeList.PaymentTypeOfThisChannelExceedLimitMONTH,
                                L("PaymentTypeOfThisChannelExceedLimitByMONTH"));
                        }
                    }
                    var yearLimit = settings.Where(x => x.LimitType == "YEAR").FirstOrDefault();
                    if (yearLimit != null)
                    {
                        var redeemYearInt = CodeGeneratorUtil.ConvertDateToYear(DateTime.UtcNow);
                        decimal sumAmnt = await _giftRedeemTransactionExtRepo.GetAll()
                                                        .Where(x => !x.IsDeleted && x.TenantId == tenantId)
                                                        .Where(x => x.RedeemYearInt == redeemYearInt && x.PaymentSource == input.RedeemSource)
                                                        .Where(x => x.PaymentType == input.PaymentType)
                                                        .SumAsync(x => x.Amount);
                        if (sumAmnt + input.TotalAmount > yearLimit.LimitAmount)
                        {
                            Logger.Warn($" >> RedeemWithPaymentType >> Checking hạn mức type = 'YEAR' failed!" +
                                        $" Hạn mức là {yearLimit.LimitAmount}, hiện đã dùng tổng {sumAmnt}...");
                            throw new UserFriendlyException((int)ErrorCodeList.PaymentTypeOfThisChannelExceedLimitYEAR,
                                L("PaymentTypeOfThisChannelExceedLimitByYEAR"));
                        }
                    }
                    var customLimit = settings.Where(x => x.LimitType == "CUSTOM").FirstOrDefault();
                    if (customLimit != null)
                    {
                        decimal sumAmnt = await _giftRedeemTransactionExtRepo.GetAll()
                            .Where(x => !x.IsDeleted && x.TenantId == tenantId && x.PaymentSource == input.RedeemSource)
                            .Where(x => x.CreationTime >= customLimit.EffectiveFrom && x.CreationTime <= customLimit.EffectiveTo)
                            .Where(x => x.PaymentType == input.PaymentType)
                            .SumAsync(x => x.Amount);
                        if (sumAmnt + input.TotalAmount > customLimit.LimitAmount)
                        {
                            Logger.Warn($" >> RedeemWithPaymentType >> Checking hạn mức type = 'CUSTOM' failed!" +
                                        $" Hạn mức là {customLimit.LimitAmount}, hiện đã dùng tổng {sumAmnt}...");
                            throw new UserFriendlyException((int)ErrorCodeList.PaymentTypeOfThisChannelExceedLimit,
                                L("PaymentTypeOfThisChannelExceedLimit"));
                        }
                    }

                    // validate hạn mức theo khoảng thời gian, ( limitType là IsNullOrEmpty)
                    var timeLimit = settings.Where(x => string.IsNullOrEmpty(x.LimitType)).FirstOrDefault();
                    if (timeLimit != null)
                    {
                        decimal sumAmnt = await _giftRedeemTransactionExtRepo.GetAll()
                            .Where(x => !x.IsDeleted && x.TenantId == tenantId && x.PaymentSource == input.RedeemSource)
                            .Where(x => x.CreationTime >= timeLimit.EffectiveFrom && x.CreationTime <= timeLimit.EffectiveTo)
                            .Where(x => x.PaymentType == input.PaymentType)
                            .SumAsync(x => x.Amount);
                        if (sumAmnt + input.TotalAmount > timeLimit.LimitAmount)
                        {
                            Logger.Warn($" >> RedeemWithPaymentType >> Checking hạn mức type là null or Empty failed!" +
                                        $" Hạn mức là {timeLimit.LimitAmount}, hiện đã dùng tổng {sumAmnt}...");
                            throw new UserFriendlyException((int)ErrorCodeList.PaymentTypeOfThisChannelExceedLimit,
                                L("PaymentTypeOfThisChannelExceedLimit"));
                        }
                    }
                }
            }

            // KHUONGDV2: Check xem Mã quà được đổi có phải là mã quà nằm trong danh sách chỉ đc đổi 30d 1 lần ko
            try
            {
                var setting = await _settingsRepository
                    .FirstOrDefaultAsync(x => x.TenantId == AbpSession.TenantId && x.Name == "ListGiftOnlyAllowBuyOnce");
                var list30dString = setting?.Value ?? "";
                var listSplit = list30dString.Split(";").ToList();
                if (listSplit.Count > 0 && listSplit.Contains(input.GiftCode))
                {
                    var keyOf30D = "LOYALTY_BE30SP_" + input.MemberCode + "_" + input.GiftCode;
                    var vl = await GetCacheValueByKey(keyOf30D);
                    if (vl == "TRUE")
                    {
                        throw new UserFriendlyException((int)ErrorCodeMessageGiftTransactions.OnlyAllowUserBuyOnce,
                            langMutil("OnlyAllowUserBuyOnce"));
                    }

                    var validateQuantityOverRedis = input.Quantity ?? 0;

                    if (validateQuantityOverRedis > 1)
                    {
                        throw new UserFriendlyException((int)ErrorCodeMessageGiftTransactions.GiftOnlyAllowQuantity1,
                            langMutil("GiftOnlyAllowQuantity1"));
                    }
                }
            }
            catch (UserFriendlyException e)
            {
                throw e;
            }
            catch (Exception e)
            {
                // EMPTY
                Logger.Error("Error when check redis of BE gift >> " + e.Message);
            }


            var connectionString = _appConfigurationAccessor.Configuration[$"ConnectionStrings:{SaaSConsts.ConnectionStringName}"];
            var transactionCode = input.TransactionCode;
            if (transactionCode == string.Empty || string.IsNullOrWhiteSpace(transactionCode))
            {
                transactionCode = CodeGeneratorUtil.GenerateGiftTxCode();
            }
            CheckExistedTransaction(transactionCode, connectionString);
            var giftParamQuery = new Dictionary<string, object>
            {
                { "@tenantId",  AbpSession.TenantId ?? 0 },
                { "@Code",  input.GiftCode }
            };

            //Lấy quà từ Redis nếu tồn tại hoặc lấy quà từ DB và lưu lên Redis nếu chưa tồn tại
            var gift = await GetGiftFromRedisOrDB(input.GiftCode, connectionString, giftParamQuery);
            // Same condition nhưng chuyển lên đây để check được failure luôn không cần tốn DB query với case khác.
            var dateTimeRun = DateTime.Parse(input.Date);
            DateTime datetimeRequest = dateTimeRun;
            if (gift == null)
            {
                throw new UserFriendlyException((int)ErrorCodeMessageGiftTransactions.GiftCodeIsNotValid, langMutil("GiftCodeIsNotValid"));
            }
            if (gift.EffectiveFrom > datetimeRequest || gift.EffectiveTo < datetimeRequest)
            {
                throw new UserFriendlyException((int)ErrorCodeMessageGiftTransactions.CanNotRedeemAtThisTime, langMutil("CanNotRedeemAtThisTime"));
            }
            if (gift.Status == "I")
            {
                throw new UserFriendlyException((int)ErrorCodeMessageGiftTransactions.GiftDisabled, langMutil("GiftDisabled"));
            }
            if (input.Quantity < 1 || datetimeRequest <= DateTime.MinValue || string.IsNullOrEmpty(input.GiftCode)
                || string.IsNullOrEmpty(input.MemberCode))
            {
                throw new UserFriendlyException((int)ErrorCodeMessageGiftTransactions.ErrorForInput, langMutil("ErrorForInput"), langMutil("ErrorForInput"));
            }
            if (gift != null)
            {
                // Chỉ validate khi quà không thuộc flash sale 
                if (string.IsNullOrWhiteSpace(input.FlashSaleProgramCode))
                {
                    if (!CheckMaxQuantityPerRedemption(gift.MaxQuantityPerRedemption, input.Quantity.Value))
                    {
                        throw new UserFriendlyException((int)ErrorCodeMessageGiftTransactions.ErrorMaxQuantityPerRedemption, langMutil("MaxQuantityPerRedemptionIsNotValid"));
                    }

                    if (!(await CheckMaxQuantityPerUser(input.MemberCode, gift.Id, gift, input.Quantity.Value)))
                    {
                        throw new UserFriendlyException((int)ErrorCodeMessageGiftTransactions.ErrorMaxQuantityPerUser, langMutil("ErrorMaxQuantityPerUserIsNotValid"));
                    }
                }
            }

            // End refactoring

            // Check gift xem cho  phep PartnerName redeem khong
            var cateCode = gift.GiftCategoryCode;
            if (!string.IsNullOrEmpty(cateCode))
            {
                input.RedeemSource = GetRedeemSource(input.RedeemSource);
                var giftCategoryChannelCode = input.RedeemSource;
                if (input.RedeemSource == "VPB")
                {
                    giftCategoryChannelCode = "vpbank";
                }
                var channelInfo = await GetCachedChannel(giftCategoryChannelCode);
                if (channelInfo == null)
                {
                    throw new UserFriendlyException((int)ErrorCodeList.GiftCategoryChannelNotAvailable, langMutil("GiftCategoryChannelNotAvailable"));
                }

                var cateObj = await _giftCategoryRepository.GetAll().Where(x => x.Code == cateCode)
                    .FirstOrDefaultAsync();

                if (cateObj == null ||
                    (cateObj.Channel != null && !cateObj.Channel.Contains(";" + channelInfo.Id + ";")))
                {
                    throw new UserFriendlyException((int)ErrorCodeList.CategoryNotAllowed, langMutil("CategoryNotAllowed"));
                }
            }

            // End check

            var parameters = new Dictionary<string, object>
            {
                { "@tenantId",  AbpSession.TenantId ?? 0 },
                { "@Code",  input.MemberCode }
            };
            MemberInfo customerDto = null;
            var result = _sqlExecuteManager.Query<MemberInfo>(connectionString,
                     @"SELECT m.Id, m.Code, m.FullRegionCode, m.FullMemberTypeCode, m.FullChannelTypeCode, 
                        m.RankTypeCode, mi.Id as IdLoyaltyInfo, r.Code AS RankCode, m.Gender, 
                        m.Status, m.NotificationSetting, m.FirstName, m.LastName, m.Phone
                    FROM `Member` AS m
                    LEFT JOIN `MemberLoyaltyInfo` as mi
                    ON m.MemberLoyaltyInfoId = mi.Id
                    LEFT JOIN `Rank` AS r
                    ON r.id = mi.RankId
                    WHERE m.IsDeleted = FALSE AND m.TenantId = @tenantId AND m.Code = @Code limit 1"
                    , parameters);
            if (result.Count() > 0)
                customerDto = result[0];
            if (customerDto == null || customerDto.IdLoyaltyInfo == 0)
            {
                throw new UserFriendlyException((int)ErrorCodeMessageGiftTransactions.MemberCodeInvalid, langMutil("MemberCodeInvalid"));
            }

            var isAlowCreateRedeem = await _memberActivityRuleAppService.CheckActivityOfMember(new CheckActivityOfMemberDto()
            {
                MemberStatus = customerDto.Status,
                Type = MemberActivityRuleConsts.GiftRedeemType
            });

            if (!isAlowCreateRedeem)
            {
                throw new UserFriendlyException((int)ErrorCodeMessageGiftTransactions.MemberStatusInvalid, langMutil("MemberStatusInvalid"));
            }

            if (gift.TargetAudienceId != null)
            {
                var segmentList = _segmentResultService.GetMemberCodeInSegmentMember(customerDto.Code);

                var rankCode = "";
                if (!string.IsNullOrWhiteSpace(customerDto.RankCode))
                {
                    rankCode = customerDto.RankCode;
                }

                var listSettingCode = new Dictionary<string, string>
                {
                    { "ChannelType", customerDto.FullChannelTypeCode + "," },
                    { "Region", customerDto.FullRegionCode + "," },
                    { "MemberType", customerDto.FullMemberTypeCode + "," },
                    { "RankType", customerDto.RankTypeCode + "," },
                    { "Rank", rankCode + "," },
                    { "Gender", customerDto.Gender + "," }
                };

                //Improve: Đã có list TargetAudienceDetail rồi
                var targetAudienceDetails = _settingParamFieldRepository.GetAll().AsNoTracking().Where(n => n.TargetAudienceId == gift.TargetAudienceId).ToList();
                var segmentId = targetAudienceDetails.Where(r => r.SegmentId > 0).Select(r => r.SegmentId.Value).FirstOrDefault();

                bool isFilterTargetAudience = false;

                if (segmentId == 0)
                {
                    if (targetAudienceDetails.Count != 0)
                    {
                        isFilterTargetAudience = targetAudienceDetails
                            .Where(d => listSettingCode.Keys.ToList()
                                .Contains(d.Type) &&
                                (listSettingCode.GetValueOrDefault(d.Type)
                                .StartsWith(d.FullValue + ",")))
                            .GroupBy(d => d.Type).Count() == 1;
                    }
                }
                else
                {
                    if (segmentList.Contains(segmentId.ToString()))
                    {
                        isFilterTargetAudience = true;
                    }
                }
                if (!isFilterTargetAudience)
                {
                    throw new UserFriendlyException((int)ErrorCodeMessageGiftTransactions.giftTargetAudienceNotFound, langMutil("giftTargetAudienceNotFound"));
                }
            }


            if (!string.IsNullOrWhiteSpace(input.FlashSaleProgramCode))
            {
                await ValidationTranferGiftFlashSale(input);
            }
            else
            {
                decimal requestGiftCoin = (decimal)(input.TotalAmount / input.Quantity);
                if (requestGiftCoin != gift.RequiredCoin)
                {
                    throw new UserFriendlyException((int)ErrorCodeMessageGiftTransactions.RequestGiftRequiredCoinIsNotValid, $"{LocalizationManager.GetString(PointConst.LocalizationSourceName, langMutil("RequestGiftRequiredCoinIsNotValid"))}");
                }

                if (gift.RemainingQuantity < input.Quantity)
                {
                    throw new UserFriendlyException((int)ErrorCodeMessageGiftTransactions.OutOfGift, langMutil("OutOfGift"));
                }
            }

            var vendorPrice = gift?.VendorPrice ?? 0;
            var saleInPrice = new decimal();
            var thirdPartyGiftCategoryId = string.Empty;
            if (gift.Is3rdPartyGift)
            {
                var thirdPartyCategory = _giftCategoryRepository.GetAll().AsNoTracking().Include(t => t.Vendor).FirstOrDefault(t => t.Code == gift.ThirdPartyCategoryCode);

                // Check status of 3rd party vendor
                if (thirdPartyCategory.Status != "A"
                    || thirdPartyCategory.Vendor.Status != "A")
                {
                    throw new UserFriendlyException((int)ErrorCodeMessageGiftTransactions.GiftDisabled, L("GiftDisabled"));
                }
                saleInPrice = vendorPrice / (decimal)thirdPartyCategory.Vendor.CoinPriceExchangeRate;
                thirdPartyGiftCategoryId = thirdPartyCategory.ThirdPartyGiftCategoryId;
            }
            var quantity = input.Quantity.Value;
            var requireCoin = gift.RequiredCoin;
            var totalRequireCoin = requireCoin * quantity;
            var giftPerTransaction = gift.IsEGift ? 1 : quantity;
            var member = customerDto;
            var giftUpdate = gift;
            var expirationReward = await GetExpirationSetting(datetimeRequest); // expirationReward không được dùng bên ngoài nhưng bên trong có throw exception nên không thể bỏ hàm này

            // Get detailed info for 1 times
            string address = "", shippingLocation = "", fullName = "", phone = "", email = "", deliveryNote = "", gender = "",
                cityId = "", districtId = "", wardId = "";
            if (!string.IsNullOrEmpty(input.Description))
            {
                try
                {
                    // Set addition info
                    var detailInfo = JObject.Parse(input.Description);
                    var fullAddress = detailInfo.GetValue("shipAddress", StringComparison.InvariantCultureIgnoreCase)?.ToString() ?? "";
                    var arr = fullAddress.Split(',').Select(t => t.Trim()).ToList();
                    address = arr.FirstOrDefault();
                    if (arr.Count > 1)
                    {
                        shippingLocation = string.Join(", ", arr.Skip(1));
                    }
                    fullName = detailInfo.GetValue("fullName", StringComparison.InvariantCultureIgnoreCase)?.ToString() ?? "";
                    phone = detailInfo.GetValue("phone", StringComparison.InvariantCultureIgnoreCase)?.ToString() ?? "";
                    email = detailInfo.GetValue("email", StringComparison.InvariantCultureIgnoreCase)?.ToString() ?? "";
                    deliveryNote = detailInfo.GetValue("note", StringComparison.InvariantCultureIgnoreCase)?.ToString() ?? "";
                    gender = detailInfo.GetValue("gender", StringComparison.InvariantCultureIgnoreCase)?.ToString() ?? "";

                    cityId = detailInfo.GetValue("cityId", StringComparison.InvariantCultureIgnoreCase)?.ToString() ?? "";
                    districtId = detailInfo.GetValue("districtId", StringComparison.InvariantCultureIgnoreCase)?.ToString() ?? "";
                    wardId = detailInfo.GetValue("wardId", StringComparison.InvariantCultureIgnoreCase)?.ToString() ?? "";
                    if (!string.IsNullOrEmpty(email))
                    {
                        email = email.Trim();
                        if (email == "null")
                        {
                            email = "";
                        }
                    }
                    // email = "" always (HOT FIX Aug 16 2022)
                    email = "";
                }
                catch (Exception)
                {
                }
            }
            // for merchantid
            string merchantCache = _cacheMemory.Get<string>(typeof(TenantIntegrationSetting));
            var merchantId = 0;
                var Is3rdType = string.Empty;
                var merchant = new TenantIntegrationSetting();
                var merchantFillter = new Dictionary<string, object>
                {
                    { "@tenantId",  gift.TenantId }
                };
                var merchantFillterVendor = new Dictionary<string, object>
                {
                    { "@tenantId",  gift.TenantId },
                    { "@vendorId",  gift.VendorId }
                };
                var giftMerchant = _sqlExecuteManager.Query<ThirdPartyGiftVendorDto>(connectionString,
                         @"SELECT Id, MerchantId, Type, SendMailVendor, MailVendor, SendSms, VendorName
                        FROM `ThirdPartyGiftVendor`
                        WHERE IsDeleted = FALSE AND TenantId = @tenantId AND Id = @vendorId limit 1"
                        , merchantFillterVendor);
                if (gift.Is3rdPartyGift || (giftMerchant.Count > 0 && giftMerchant[0].Type == ThirdPartyVendorType.Special))
                {
                    // Get merchant id from gift store vendor
                    string merchantGiftCache = _cacheMemory.Get<string>("GiftVendorId" + gift.VendorId);
                    if (merchantGiftCache == null)
                    {
                        if (giftMerchant.Count >= 0)
                        {
                            merchantId = giftMerchant[0].MerchantId;
                            Is3rdType = giftMerchant[0].Type;

                            var cacheSettingItem = new ThirdPartyGiftVendorDto()
                            {
                                MerchantId = giftMerchant[0].MerchantId,
                                Id = giftMerchant[0].Id,
                                Type = giftMerchant[0].Type,
                                MailVendor = giftMerchant[0].MailVendor,
                                SendMailVendor = giftMerchant[0].SendMailVendor,
                                SendSms = giftMerchant[0].SendSms,
                                VendorName = giftMerchant[0].VendorName
                            };
                            _cacheMemory.Set("GiftVendorId" + gift.VendorId, JsonConvert.SerializeObject(cacheSettingItem),
                                new MemoryCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromHours(6)));
                        }
                    }
                    else
                    {
                        var merchantData = JsonConvert.DeserializeObject<ThirdPartyGiftVendorDto>(merchantGiftCache);
                        merchantId = merchantData.MerchantId;
                        Is3rdType = merchantData.Type;
                    }
                    // Default merchant from setting
                    if (merchantId == 0)
                    {
                        merchantId = GetMerchantId(merchantCache, merchantFillter, connectionString);
                    }
                }
                else
                {
                    merchantId = GetMerchantId(merchantCache, merchantFillter, connectionString);
                }
            if (input.MerchantIdRedeem.HasValue && input.MerchantIdRedeem > 0)
            {
                merchantId = await GetMerchantIdByVendorId(gift.VendorId ?? 0);
                if (merchantId != input.MerchantIdRedeem)
                {
                    throw new UserFriendlyException(5001, L("MerchantAllowShowInvalid"));
                }
            }
            // end merchantid

            try
            {
                using (var unitOfWork = _unitOfWorkManager.Begin(TransactionScopeOption.RequiresNew))
                {
                    var giftTransaction = new CreateGiftRedeemTransaction
                    {
                        BuyerCode = member.Code,
                        GiftCode = giftUpdate.Code,
                        TenantId = giftUpdate.TenantId,
                        GiftId = giftUpdate.Id,
                        GiftName = giftUpdate.Name,
                        Date = datetimeRequest,
                        Quantity = giftPerTransaction,
                        TotalCoin = input.TotalAmount,
                        Description = input.Description,
                        MemberId = member.Id,
                        TransactionCode = transactionCode,
                        CreationTime = DateTime.UtcNow,
                        VendorId = giftUpdate.VendorId,
                        RecipientName = !string.IsNullOrEmpty(fullName) ? fullName : "",
                        RecipientNameSearch = !string.IsNullOrEmpty(fullName) ? StringUtilHelper.RemoveAccentVietnamese(fullName) : "",
                        RecipientPhone = !string.IsNullOrEmpty(phone) ? phone : "",
                        RedeemSource = GetRedeemSource(input.RedeemSource),
                        HasPendingTransaction = true,
                        MerchantId = merchantId,
                        Status = ProcessingVendorStatus.Pending.ToString(),
                        // Nếu là quà 3rd thì khi tạo đơn hàng tạm status xem là pending. Nếu đơn thanh toán thành công sẽ update lại status = Success.
                        // Nếu thanh toán hk thành công thì vẫn là pending
                        CardCode = input.CardCode,
                        PaymentSource = input.PaymentSource,
                        PaymentType = input.PaymentType
                    };
                    if (giftUpdate.Is3rdPartyGift)
                    {
                        giftTransaction.SaleInPrice = ToInt(saleInPrice);
                        giftTransaction.ThirdPartyGiftCategoryId = thirdPartyGiftCategoryId;
                    }

                    giftTransaction.LastModificationTime = giftTransaction.CreationTime;
                    _createGiftTransactionRepository.InsertAndGetId(giftTransaction);
                    await _unitOfWorkManager.Current.SaveChangesAsync();
                    unitOfWork.Complete();
                }
            }
            catch (Exception ex)
            {
                Logger.Info("Process insert transaction fail with error : " + ex.Message);
                return new VerifyAndCreateRedeemOrderOutput()
                {
                    IsSuccess = false,
                    Exception = ex.Message,
                };
            }

            return new VerifyAndCreateRedeemOrderOutput()
            {
                IsSuccess = true,
                TransactionCode = transactionCode,
            };
        }

        public async Task<UpdateErrorWhenCreateRedeemPaymentOutput> HandleErrorWhenCreateRedeemPayment(UpdateErrorWhenCreateRedeemPayment input)
        {
            if (!string.IsNullOrWhiteSpace(input.TransactionCode))
            {
                var transactionRedeem = await _createGiftTransactionRepository.GetAll()
                .Include(x => x.Gift)
                .Where(p => p.TransactionCode == input.TransactionCode && p.Status != ProcessingVendorStatus.Success.ToString()).FirstOrDefaultAsync();
                if (transactionRedeem != null)
                {
                    _createGiftTransactionRepository.Update(transactionRedeem.Id, trx =>
                    {
                        trx.ProcessingErrorCode = input.ErrorCode;
                        trx.ProcessingErrorMessage = input.ErrorMessage;
                        if (!string.IsNullOrWhiteSpace(input.Status))
                        {
                            trx.Status = input.Status;
                        }
                        Task.FromResult(0);
                    });
                    return new UpdateErrorWhenCreateRedeemPaymentOutput()
                    {
                        IsSuccess = true,
                        TransactionCode = input.TransactionCode,
                    };
                }
                else
                {
                    Logger.Info("Cannot find transaction transaction when update status");
                    return new UpdateErrorWhenCreateRedeemPaymentOutput()
                    {
                        IsSuccess = false,
                        TransactionCode = input.TransactionCode,
                    };
                }
            }
            return new UpdateErrorWhenCreateRedeemPaymentOutput()
            {
                IsSuccess = false,
                TransactionCode = input.TransactionCode,
            };
        }

        // For error transaction v2
        [AbpAuthorize(PointPermissions.Pages_GiftRedeemErrorTransaction)]
        public async Task<PagedResultDto<GetRedeemErrorTransactionOutput>> GetAllErrorTransactionRedeem(GetProcessingVendorTransactionInput input)
        {

            var root3rdCategory = new List<string>();
            if (!string.IsNullOrEmpty(input.ThirdPartyGiftCategoryId))
            {
                var filter3rdCategoryId = await _integrationCategoryMapping.GetAll().Where(x => x.VendorId == input.VendorId).ToListAsync();
                root3rdCategory = filter3rdCategoryId.Where(x => x.IntegrationId.Equals(input.ThirdPartyGiftCategoryId)
                        || (x.ParentId == input.ThirdPartyGiftCategoryId)).Select(x => x.IntegrationId).ToList();

                var childCategory = filter3rdCategoryId.Where(x => root3rdCategory.Contains(x.ParentId ?? "")).Select(x => x.IntegrationId).ToList();
                if (childCategory != null)
                {
                    root3rdCategory = root3rdCategory.Union(childCategory).ToList();
                }
            }
            var tenantInternal = _commonAppService.GetTenantInternalId();
            var filteredMemberCode = string.Empty;
            var hasFilterMember = !string.IsNullOrWhiteSpace(input.MemberCodeFilter)
                                  || !string.IsNullOrWhiteSpace(input.NameFilter)
                                  || !string.IsNullOrWhiteSpace(input.IdCard)
                                  || !string.IsNullOrWhiteSpace(input.PhoneFilter);
            if (hasFilterMember)
            {
                using (CurrentUnitOfWork.SetTenantId(tenantInternal))
                {
                    filteredMemberCode = await _memberRepository.GetAll()
                    .WhereIf(!string.IsNullOrWhiteSpace(input.MemberCodeFilter), x => x.Code == input.MemberCodeFilter)
                    .WhereIf(!string.IsNullOrWhiteSpace(input.NameFilter), x => x.NameSearch.ToLower().Contains(StringUtilHelper.RemoveAccentVietnamese(input.NameFilter.ToLower())))
                    .WhereIf(!string.IsNullOrWhiteSpace(input.PhoneFilter), x => x.Phone == input.PhoneFilter)
                    .WhereIf(!string.IsNullOrWhiteSpace(input.IdCard), x => x.IdCard == input.IdCard)
                    .Select(x => x.Code).FirstOrDefaultAsync();
                }
                if (filteredMemberCode == null)
                {
                    return new PagedResultDto<GetRedeemErrorTransactionOutput>();
                }
            }
            var listIdMerchantEVoucher = _thirdPartyGiftVendorRepository.GetAll().Where(x => x.Type == "Topup").Select(x => x.Id).ToList();
            var merchantIdEvoucher = input.MerchantNhanId;
            if (input.MerchantNhanId.HasValue && !listIdMerchantEVoucher.Contains(input.MerchantNhanId.Value))
            {
                merchantIdEvoucher = 0;
            }

            var filteredGiftTransactions = _createGiftTransactionRepository.GetAll()
                           .WhereIf(!string.IsNullOrWhiteSpace(input.Filter), e => e.BuyerCode.Contains(input.Filter.Trim()))
                           .WhereIf(!string.IsNullOrWhiteSpace(input.ProcessingVendorStatus), e => e.Status == input.ProcessingVendorStatus)
                           .WhereIf(!string.IsNullOrWhiteSpace(input.GiftFilter), e => e.GiftCode.Contains(input.GiftFilter.Trim()) || e.GiftName.Contains(input.GiftFilter.Trim()))
                           .WhereIf(!string.IsNullOrWhiteSpace(input.CodeFilter), e => e.TransactionCode.Contains(input.CodeFilter.Trim()))
                           .WhereIf(!string.IsNullOrWhiteSpace(input.MemberCodeFilter), e => e.BuyerCode.ToLower() == input.MemberCodeFilter.ToLower().Trim())
                           .WhereIf(!string.IsNullOrWhiteSpace(input.StatusFilter), e => e.Status.ToLower() == input.StatusFilter.ToLower().Trim())
                           .WhereIf(input.FromDateFilter != null, e => e.Date.Value >= input.FromDateFilter.Value)
                           .WhereIf(input.ToDateFilter != null, e => e.Date.Value <= input.ToDateFilter.Value.AddDays(1))
                           .WhereIf(input.VendorId != null && input.VendorId != -1 && input.VendorId != 0, e => e.Gift.VendorId == input.VendorId)
                           .WhereIf(input.VendorId == -1, e => e.Gift.Vendor == "LinkId" || e.Gift.Vendor == null || e.Gift.Vendor == "")
                           .WhereIf(merchantIdEvoucher > 0, e => e.VendorId == merchantIdEvoucher)
                           .WhereIf(!string.IsNullOrEmpty(input.ThirdPartyGiftCategoryId) && root3rdCategory.Any(), e => root3rdCategory.Contains(e.ThirdPartyGiftCategoryId ?? ""))
                           .WhereIf(!string.IsNullOrWhiteSpace(input.RecipientNameFilter), e => (EF.Functions.Like(e.RecipientNameSearch.ToLower(), $"%{input.RecipientNameFilter.ToLower()}%")))
                           .WhereIf(!string.IsNullOrWhiteSpace(input.RecipientPhoneFilter), x => x.RecipientPhone == input.RecipientPhoneFilter)
                           .WhereIf(!string.IsNullOrWhiteSpace(input.RedeemSourceFilter), e => e.RedeemSource == input.RedeemSourceFilter)
                           .Include(g => g.Gift)
                           .WhereIf(input.IsEGift != -1, g => g.Gift.IsEGift == Convert.ToBoolean(input.IsEGift))
                           .WhereIf(!string.IsNullOrWhiteSpace(filteredMemberCode), g => filteredMemberCode == g.BuyerCode)
                           .WhereIf(input.IsTopup, g => g.Gift.GiftType == "TopupEvoucher")
                           .Where(e => e.HasPendingTransaction == true)
                           .WhereIf(!string.IsNullOrWhiteSpace(input.PaymentSource), x => x.PaymentSource == input.PaymentSource);
            if(!string.IsNullOrWhiteSpace(input.PaymentType))
            {
                if (input.PaymentType == "TOKEN")
                {
                    // TOKEN là core của hệ thống, nên các giao dịch hồi trước khi chưa có paymemntType là null
                    filteredGiftTransactions = filteredGiftTransactions.Where(x => x.PaymentType == input.PaymentType || string.IsNullOrEmpty(input.PaymentType));
                }
                if(input.PaymentType == "CASH")
                {
                    filteredGiftTransactions = filteredGiftTransactions.Where(x => x.PaymentType == input.PaymentType);
                }    
            }    
            var query = (from o in filteredGiftTransactions
                         orderby o.Id descending
                         select new GetRedeemErrorTransactionOutput()
                         {
                             GiftTransaction = ObjectMapper.Map<RedeemErrorTransactionDto>(o)
                         })
                         .GroupBy(t => t.GiftTransaction.TransactionCode)
                         .Select(x => new GetRedeemErrorTransactionOutput
                         {
                             GiftTransaction = ObjectMapper.Map<RedeemErrorTransactionDto>(x.First().GiftTransaction),
                             TotalCoin = x.Sum(y => y.GiftTransaction.TotalCoin),
                             Quantity = x.Sum(q => q.GiftTransaction.Quantity)
                         });

            var totalCount = await query.CountAsync();

            var giftTransactions = await query
                //.OrderBy(input.Sorting ?? "giftTransaction.id desc")
                .PageBy(input)
                .ToListAsync();

            var giftIds = giftTransactions.Select(t => t.GiftTransaction.GiftId).ToList();
            var giftInfos = await _giftInforRepository.GetAll().Where(t => giftIds.Contains(t.Id)).ToListAsync();
            var memberCodes = giftTransactions.Select(t => t.GiftTransaction.BuyerCode).Distinct().ToList();
            memberCodes.AddRange(giftTransactions.Select(t => t.GiftTransaction.OwnerCode).Distinct().ToList());
            memberCodes = memberCodes.Select(x => x).Distinct().ToList();
            // get member by internal tenant
            var members = new List<SelectMemberShortInfoSupportSme>();
            using (CurrentUnitOfWork.SetTenantId(tenantInternal))
            {
                members = await _secondaryCustomerRepository.GetAll()
                    .Select(t => new SelectMemberShortInfoSupportSme
                    {
                        Code = t.Code,
                        FirstName = t.FirstName,
                        LastName = t.LastName,
                        Phone = t.Phone,
                        Address = t.Address,
                        Email = t.Email,
                        Gender = t.Gender,
                        IdCard = t.IdCard,
                        Type = t.Type,
                    })
                    .Where(t => memberCodes.Contains(t.Code))
                    .ToListAsync();
                foreach (var selectMemberShortInfoSupportSme in members)
                {
                    selectMemberShortInfoSupportSme.Name = selectMemberShortInfoSupportSme.FirstName;
                    if (selectMemberShortInfoSupportSme.Type != "SME" && string.IsNullOrWhiteSpace(selectMemberShortInfoSupportSme.LastName) == false)
                    {
                        selectMemberShortInfoSupportSme.Name += " " + selectMemberShortInfoSupportSme.LastName;
                    }
                }
            }
            var allVendors = await _thirdPartyGiftVendorRepository.GetAllListAsync();
            var listBrandIds = giftInfos.Where(x => x.BrandId != null).Select(x => x.BrandId).ToList();
            var listBandObjects =
                await _brandInforRepository.GetAll().Where(x => x.IsActive && !x.IsDeleted && listBrandIds.Contains(x.Id)).ToListAsync();
            // Get list userId
            var listUserId = giftTransactions.Where(x => x.GiftTransaction.LastUserIdUpdateProcessing.HasValue).Select(x => x.GiftTransaction.LastUserIdUpdateProcessing).ToList();
            var listUsers = await UserManager.Users.Where(x => listUserId.Any(y => y == x.Id)).ToListAsync();
            var glstTransactionCode = giftTransactions.Select(x => x.GiftTransaction.TransactionCode);
            foreach (var giftTransaction in giftTransactions)
            {
                if (!string.IsNullOrEmpty(giftTransaction.GiftTransaction.Description))
                {
                    try
                    {
                        var obj = JObject.Parse(giftTransaction.GiftTransaction.Description);

                        giftTransaction.GiftTransaction.RecipientName = Convert.ToString(obj.GetValue("fullName", StringComparison.InvariantCultureIgnoreCase));
                        giftTransaction.GiftTransaction.RecipientPhone = Convert.ToString(obj.GetValue("phone", StringComparison.InvariantCultureIgnoreCase));
                        giftTransaction.GiftTransaction.RecipientAddress = Convert.ToString(obj.GetValue("shipAddress", StringComparison.InvariantCultureIgnoreCase));
                        giftTransaction.Note = Convert.ToString(obj.GetValue("note", StringComparison.InvariantCultureIgnoreCase));
                        giftTransaction.GiftTransaction.RecipientGender = Convert.ToString(obj.GetValue("gender", StringComparison.InvariantCultureIgnoreCase));
                    }
                    catch (Exception)
                    {
                    }
                }

                var member = members.FirstOrDefault(t => t.Code == giftTransaction.GiftTransaction.BuyerCode);
                giftTransaction.GiftTransaction.MemberName = !string.IsNullOrWhiteSpace(giftTransaction.GiftTransaction.MemberName) ? giftTransaction.GiftTransaction.MemberName : member?.Name;
                giftTransaction.GiftTransaction.Phone = !string.IsNullOrWhiteSpace(giftTransaction.GiftTransaction.Phone) ? giftTransaction.GiftTransaction.Phone : member?.Phone;
                giftTransaction.GiftTransaction.Address = !string.IsNullOrWhiteSpace(giftTransaction.GiftTransaction.Address) ? giftTransaction.GiftTransaction.Address : member?.Address;
                giftTransaction.Email = !string.IsNullOrWhiteSpace(giftTransaction.Email) ? giftTransaction.Email : member?.Email;
                giftTransaction.GiftTransaction.RecipientGender = !string.IsNullOrWhiteSpace(giftTransaction.GiftTransaction.RecipientGender) ? giftTransaction.GiftTransaction.RecipientGender : member?.Gender;
                // Get 3rd party gift info
                var giftInfo = giftInfos.FirstOrDefault(t => t.Id == giftTransaction.GiftTransaction.GiftId);
                giftTransaction.GiftTransaction.IsEGift = giftInfo.IsEGift;
                giftTransaction.GiftTransaction.BuyerIdCard = member.IdCard;
                if (giftInfo != null)
                {
                    if (giftInfo.GiftType != "TopupEvoucher")
                    {
                        giftTransaction.BrandName = listBandObjects.FirstOrDefault(t => t.Id == giftInfo.BrandId)?.Name ?? "";
                        giftTransaction.GiftTransaction.VendorId = giftInfo.VendorId;
                        giftTransaction.GiftTransaction.VendorName = allVendors.FirstOrDefault(t => t.Id == giftInfo.VendorId)?.VendorName ?? giftInfo.Vendor;
                        giftTransaction.GiftTransaction.IsEGift = giftInfo.IsEGift;
                        giftTransaction.ThirtPartyBrandName = giftInfo.ThirdPartyBrandName;
                    }
                    else
                    {
                        giftTransaction.EvoucherMerchantNhan =
                            allVendors.FirstOrDefault(t => t.Id == giftTransaction.GiftTransaction.VendorId)?.VendorName ?? "";
                        giftTransaction.GiftTransaction.VendorId = null;
                        giftTransaction.GiftTransaction.VendorName = null;
                    }
                }
                var userUpdateProcess = listUsers.FirstOrDefault(x => x.Id == giftTransaction.GiftTransaction.LastUserIdUpdateProcessing);
                giftTransaction.LastUserUpdateProcessing = userUpdateProcess == null ? null : userUpdateProcess.UserName;

                if(string.IsNullOrEmpty(giftTransaction.GiftTransaction.PaymentType))
                {
                    giftTransaction.GiftTransaction.PaymentType = "TOKEN";
                }    
               
            }

            return new PagedResultDto<GetRedeemErrorTransactionOutput>(
                totalCount,
                giftTransactions
            );
        }

        [AbpAuthorize(PointPermissions.Pages_GiftRedeemErrorTransaction)]
        public async Task<PointFileDto> GetAllErrorTransactionRedeemToExcel(GetAllErrorTransactionsForExcelInput input)
        {
            var tenantInternal = _commonAppService.GetTenantInternalId();
            var filteredMemberCode = string.Empty;
            var hasFilterMember = !string.IsNullOrWhiteSpace(input.MemberCodeFilter) || !string.IsNullOrWhiteSpace(input.NameFilter) || !string.IsNullOrWhiteSpace(input.PhoneFilter);
            var listIdMerchantEVoucher = _thirdPartyGiftVendorRepository.GetAll().Where(x => x.Type == "Topup").Select(x => x.Id).ToList();
            var merchantIdEvoucher = input.MerchantNhanId;
            if (input.MerchantNhanId.HasValue && !listIdMerchantEVoucher.Contains(input.MerchantNhanId.Value))
            {
                merchantIdEvoucher = 0;
            }
            if (hasFilterMember)
            {
                using (CurrentUnitOfWork.SetTenantId(tenantInternal))
                {
                    filteredMemberCode = await _memberRepository.GetAll()
                    .WhereIf(!string.IsNullOrWhiteSpace(input.MemberCodeFilter), x => x.Code == input.MemberCodeFilter)
                    .WhereIf(!string.IsNullOrWhiteSpace(input.NameFilter), x => x.NameSearch.ToLower().Contains(StringUtilHelper.RemoveAccentVietnamese(input.NameFilter.ToLower())))
                    .WhereIf(!string.IsNullOrWhiteSpace(input.PhoneFilter), x => x.Phone == input.PhoneFilter)
                    .Select(x => x.Code).FirstOrDefaultAsync();
                }
                if (filteredMemberCode == null)
                {
                    return _giftTransactionExcelExporter.ExportErrorTransactionToFile(new List<GetErrorTransactionRedeemForExcel>());
                }
            }
            var filteredGiftTransactions = _createGiftTransactionRepository.GetAll()
                           .WhereIf(!string.IsNullOrWhiteSpace(input.Filter), e => e.BuyerCode.Contains(input.Filter.Trim()))
                           .WhereIf(!string.IsNullOrWhiteSpace(input.ProcessingVendorStatus), e => e.Status == input.ProcessingVendorStatus)
                           .WhereIf(!string.IsNullOrWhiteSpace(input.GiftFilter), e => e.GiftCode.Contains(input.GiftFilter.Trim()) || e.GiftName.Contains(input.GiftFilter.Trim()))
                           .WhereIf(!string.IsNullOrWhiteSpace(input.CodeFilter), e => e.TransactionCode.Contains(input.CodeFilter.Trim()))
                           .WhereIf(!string.IsNullOrWhiteSpace(input.MemberCodeFilter), e => e.BuyerCode.ToLower() == input.MemberCodeFilter.ToLower().Trim())
                           .WhereIf(!string.IsNullOrWhiteSpace(input.StatusFilter), e => e.Status.ToLower() == input.StatusFilter.ToLower().Trim())
                           .WhereIf(input.FromDateFilter != null, e => e.Date.Value >= input.FromDateFilter.Value)
                           .WhereIf(input.ToDateFilter != null, e => e.Date.Value <= input.ToDateFilter.Value.AddDays(1))
                           .WhereIf(input.VendorId != null && input.VendorId != -1 && input.VendorId != 0, e => e.Gift.VendorId == input.VendorId)
                           .WhereIf(input.VendorId == -1, e => e.Gift.Vendor == "LinkId" || e.Gift.Vendor == null || e.Gift.Vendor == "")
                           .WhereIf(merchantIdEvoucher > 0, e => e.VendorId == merchantIdEvoucher)
                           .WhereIf(!string.IsNullOrWhiteSpace(input.RecipientNameFilter), e => (EF.Functions.Like(e.RecipientNameSearch.ToLower(), $"%{input.RecipientNameFilter.ToLower()}%")))
                           .WhereIf(!string.IsNullOrWhiteSpace(input.RecipientPhoneFilter), x => x.RecipientPhone == input.RecipientPhoneFilter)
                           .WhereIf(!string.IsNullOrWhiteSpace(input.RedeemSourceFilter), e => e.RedeemSource == input.RedeemSourceFilter)
                           .Include(g => g.Gift)
                           .WhereIf(input.IsEGift != -1, g => g.Gift.IsEGift == Convert.ToBoolean(input.IsEGift))
                           .WhereIf(!string.IsNullOrWhiteSpace(filteredMemberCode), g => filteredMemberCode == g.BuyerCode)
                           .WhereIf(input.IsTopup, g => g.Gift.GiftType == "TopupEvoucher")
                           .Where(e => e.HasPendingTransaction == true);

            var query = (from o in filteredGiftTransactions
                         select new GetErrorTransactionRedeemForExcel()
                         {
                             GiftTransaction = ObjectMapper.Map<RedeemErrorTransactionDto>(o)
                         })
                         .GroupBy(t => t.GiftTransaction.TransactionCode)
                         .Select(x => new GetErrorTransactionRedeemForExcel
                         {
                             GiftTransaction = ObjectMapper.Map<RedeemErrorTransactionDto>(x.First().GiftTransaction),
                             TotalCoin = x.Sum(y => y.GiftTransaction.TotalCoin),
                             Quantity = x.Sum(q => q.GiftTransaction.Quantity)
                         });
            var giftTransactions = await query.OrderBy("giftTransaction.id desc").ToListAsync();

            var giftIds = giftTransactions.Select(t => t.GiftTransaction.GiftId).ToList();
            var giftInfos = await _giftInforRepository.GetAll().Where(t => giftIds.Contains(t.Id)).ToListAsync();
            var vendorIds = giftInfos.Where(t => t.VendorId != null).Select(t => t.VendorId).ToList();
            var vendors = await _thirdPartyGiftVendorRepository.GetAll().Where(t => vendorIds.Contains(t.Id)).ToListAsync();
            var memberCodes = giftTransactions.Select(t => t.GiftTransaction.BuyerCode).Distinct().ToList();
            var members = new List<SelectMemberShortInfo>();
            using (CurrentUnitOfWork.SetTenantId(tenantInternal))
            {
                members = await _secondaryCustomerRepository.GetAll()
                .Select(t => new SelectMemberShortInfo
                {
                    Code = t.Code,
                    Name = t.FirstName + " " + t.LastName,
                    Phone = t.Phone,
                    Address = t.Address,
                    Email = t.Email
                })
                .Where(t => memberCodes.Contains(t.Code))
                .ToListAsync();
            }
            // Get list userId
            var listUserId = giftTransactions.Where(x => x.GiftTransaction.LastUserIdUpdateProcessing.HasValue).Select(x => x.GiftTransaction.LastUserIdUpdateProcessing).ToList();
            var listUsers = await UserManager.Users.Where(x => listUserId.Any(y => y == x.Id)).ToListAsync();
            for (int i = 0; i < giftTransactions.Count; i++)
            {
                giftTransactions[i].OrderNumber = i + 1;

                // Get 3rd party gift info
                var giftInfo = giftInfos.FirstOrDefault(t => t.Id == giftTransactions[i].GiftTransaction.GiftId);
                giftTransactions[i].GiftTransaction.VendorId = giftInfo.VendorId;
                giftTransactions[i].GiftTransaction.VendorName = vendors.FirstOrDefault(t => t.Id == giftInfo.VendorId)?.VendorName ?? giftInfo.Vendor;

                if (!string.IsNullOrEmpty(giftTransactions[i].GiftTransaction.Description))
                {
                    try
                    {
                        var obj = JObject.Parse(giftTransactions[i].GiftTransaction.Description);

                        giftTransactions[i].GiftTransaction.RecipientName = Convert.ToString(obj.GetValue("fullName", StringComparison.InvariantCultureIgnoreCase));
                        giftTransactions[i].GiftTransaction.RecipientPhone = Convert.ToString(obj.GetValue("phone", StringComparison.InvariantCultureIgnoreCase));
                        giftTransactions[i].GiftTransaction.RecipientAddress = Convert.ToString(obj.GetValue("shipAddress", StringComparison.InvariantCultureIgnoreCase));
                        giftTransactions[i].Note = Convert.ToString(obj.GetValue("note", StringComparison.InvariantCultureIgnoreCase));
                    }
                    catch (Exception)
                    {
                    }
                }
                var member = members.FirstOrDefault(t => t.Code == giftTransactions[i].GiftTransaction.BuyerCode);
                giftTransactions[i].GiftTransaction.MemberName = !string.IsNullOrWhiteSpace(giftTransactions[i].GiftTransaction.MemberName) ? giftTransactions[i].GiftTransaction.MemberName : member?.Name;
                giftTransactions[i].GiftTransaction.Phone = !string.IsNullOrWhiteSpace(giftTransactions[i].GiftTransaction.Phone) ? giftTransactions[i].GiftTransaction.Phone : member?.Phone;
                giftTransactions[i].GiftTransaction.Address = !string.IsNullOrWhiteSpace(giftTransactions[i].GiftTransaction.Address) ? giftTransactions[i].GiftTransaction.Address : member?.Address;
                giftTransactions[i].Email = !string.IsNullOrWhiteSpace(giftTransactions[i].Email) ? giftTransactions[i].Email : member?.Email;
                giftTransactions[i].GiftTransaction.IsEGift = giftInfo.IsEGift;

                var userUpdateProcess = listUsers.FirstOrDefault(x => x.Id == giftTransactions[i].GiftTransaction.LastUserIdUpdateProcessing);
                giftTransactions[i].LastUserUpdateProcessing = userUpdateProcess == null ? null : userUpdateProcess.UserName;
            }

            return _giftTransactionExcelExporter.ExportErrorTransactionToFile(giftTransactions);
        }

        protected async Task UpdateStatusTransactionRedeemPending(string transactionCode, string status)
        {
            try
            {
                if (!string.IsNullOrEmpty(transactionCode))
                {
                    var transactionRedeem = await _createGiftTransactionRepository.GetAll()
                        .Include(x => x.Gift)
                        .Where(p => p.TransactionCode == transactionCode && p.Status != TransactionRedeemStatus.Success.ToString()).FirstOrDefaultAsync();
                    if (transactionRedeem != null)
                    {
                        _createGiftTransactionRepository.Update(transactionRedeem.Id, trx =>
                        {
                            trx.Status = status;
                            if (status == TransactionRedeemStatus.Success.ToString())
                            {
                                trx.HasPendingTransaction = false;
                            }
                            Task.FromResult(0);
                        });
                    }
                    else
                    {
                        Logger.Info("Cannot find transaction transaction when update status");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"Error when update status redeem pending {ex.Message}", ex);
            }
        }
    }
}
