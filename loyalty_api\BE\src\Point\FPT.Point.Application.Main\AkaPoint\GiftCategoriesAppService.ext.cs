﻿using FPT.SaaS.AkaPoint;

using System;
using System.Linq;
using System.Linq.Dynamic.Core;
using Abp.Linq.Extensions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Abp.Domain.Repositories;
using FPT.SaaS.AkaPoint.Dtos;

using Abp.Application.Services.Dto;
using FPT.SaaS.Authorization;
using Abp.Authorization;
using Microsoft.EntityFrameworkCore;
using Abp.UI;
using Abp.Localization;
using Newtonsoft.Json;
using FPT.Point.Application.Shared.AkaPoint.ThirdParty.ThirdPartyVendor;
using FPT.SaaS.Extensions;
using FPT.Point.Application;
using Newtonsoft.Json.Serialization;
using FPT.Point.Application.Shared.AkaPoint.Dtos.ThirdParty.GiftCategory;
using System.Collections;
using AutoMapper;
using FPT.Point.Core.AkaPoint;
using Abp.Collections.Extensions;
using Castle.Core.Internal;
using Abp.Extensions;
using FPT.Point.Application.Shared.AkaPoint.Dtos.ThirdParty;
using FPT.Point.Core.Helper;
using Abp.Json;
using System.Text;
using FPT.SaaS.EventBus;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Distributed;
using FPT.Point.Dto;
using System.Globalization;
using MySql.Data.MySqlClient;
using System.Collections.ObjectModel;
using Dapper;
using FPT.Point.Application.Shared.AkaPoint.Dtos.GiftMultiLanguage;
using FPT.Point.Application.Shared.Dto;
using FPT.Point.Application.Shared.Extensions;

namespace FPT.SaaS.AkaPoint
{
    public partial class GiftCategoryAppService
    {
        private enum ErrorCode
        {
            GiftCategoryParentStatus = 101,
            ThirdPartyVendorStatus = 201,
        }

        public async Task<PagedResultDto<GetGiftCategoryForView>> GetAllCustom(GetAllGiftCategoriesFilter input)
        {
            InitListGiftCategory();
            if (!string.IsNullOrEmpty(input.Filter))
            {
                input.Filter = input.Filter.Trim().ToLower();
            }
            var filteredGiftCategories = _giftCategoryRepository.GetAll()
                        .WhereIf(!string.IsNullOrWhiteSpace(input.Filter), e => false || e.Code.ToLower().Contains(input.Filter) || e.Name.ToLower().Contains(input.Filter) || e.Description.ToLower().Contains(input.Filter))
                        .WhereIf(!string.IsNullOrWhiteSpace(input.CategoryNameFilter), e => false || e.Name.ToLower().Contains(input.CategoryNameFilter))
                        .WhereIf(!string.IsNullOrWhiteSpace(input.StatusFilter), e => false || e.Status.ToLower() == input.StatusFilter.ToLower())
                        .WhereIf(input.ParentIdFilter != null, e => e.ParentId == input.ParentIdFilter)
                        .WhereIf(input.LevelFilter != null, e => e.Level == input.LevelFilter)
                        .WhereIf(input.Is3rdPartyGiftCategory.HasValue, e => e.Is3rdPartyGiftCategory == input.Is3rdPartyGiftCategory);
            //.Where(p => p.Status.ToLower().Equals("a"));



            var query = (from o in filteredGiftCategories
                         join o1 in _giftCategoryRepository.GetAll() on o.ParentId equals o1.Id into j1
                         from s1 in j1.DefaultIfEmpty()

                         select new GetGiftCategoryForView()
                         {
                             GiftCategory = ObjectMapper.Map<GiftCategoryDto>(o)
                         ,
                             ParentNameGiftCategory = s1 == null ? "" : s1.Name.ToString(),
                         });

            // .WhereIf(!string.IsNullOrWhiteSpace(input.GiftCategoryGiftCategoryNameFilter), e => e.GiftCategoryGiftCategoryName.ToLower() == input.GiftCategoryGiftCategoryNameFilter.ToLower().Trim());

            var totalCount = await query.CountAsync();

            var giftCategories = await query
                .OrderBy("giftCategory.id desc")
                .PageBy(input)
                .ToListAsync();
            if (giftCategories.Count > 0)
            {

                //if (input.ParentIdFilter ==null)
                //{
                //    var coppyGiftCategories = giftCategories;
                //    foreach (var item in giftCategories.ToList())
                //    {
                //        var childNodes = getChildItem(item.GiftCategory.Id);

                //        foreach (var c in childNodes.ToList())
                //        {
                //            var i = giftCategories.FirstOrDefault(p => p.GiftCategory.Id == c.Id);
                //            if (i!=null)
                //            {
                //                coppyGiftCategories.Remove(i);
                //            }
                //        }

                //    }
                //    giftCategories = coppyGiftCategories;
                //}

                giftCategories.ForEach(p =>
                {
                    var imageLink = _imageLinkRepository
                            .FirstOrDefault(x => x.Code == p.GiftCategory.Code && x.Type == "GiftCategory");
                    p.GiftCategory.ImageLink = ObjectMapper.Map<ImageLinkDto>(imageLink);
                });
            }

            return new PagedResultDto<GetGiftCategoryForView>(
                totalCount,
                giftCategories
            );
        }
        
        [AbpAuthorize(AppPermissions.Pages_GiftCategory)]
        public async Task<PagedResultDto<GetGiftCategoryForView>> GetRecommendedCategories(GetRecommendedCategoriesInput input)
        {
            
            var tenantId = AbpSession.TenantId ?? 0;
            var cacheKey = "LOYALTY_GetRecommendedCategories_" + input.MemberCode;
            var cachedString = await _cache.GetStringAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(cachedString))
            {
                try
                {
                    Logger.Info(" >> CACHE HIT >> LOYALTY_GetRecommendedCategories_ " + input.MemberCode);
                    var cachedObj = JsonConvert.DeserializeObject<PagedResultDto<GetGiftCategoryForView>>(cachedString);
                    return cachedObj;
                }
                catch (Exception e)
                {
                    Logger.Error(" >> LOYALTY_GetRecommendedCategories_ >> Error cache >> " + input.MemberCode + " - Remove the cache now!");
                    await _cache.RemoveAsync(cacheKey);
                }
            }
            // MemberCode ko tồn tại thì báo lỗi
            // check member existence
            var memberCount = await _memberRepository.GetAll().Where(x =>
                !x.IsDeleted && x.TenantId == tenantId && x.Status == "A" && x.Code == input.MemberCode).AnyAsync();
            if (memberCount == false) {
                throw new UserFriendlyException(607, this.L("MemberCodeNotValid"));
            }
            // end
            var getFromRecommendationSystem = await _recommendationEngineAppService.GetRecommendedGiftsFromRecommendationSystem(input.MemberCode);
            if (getFromRecommendationSystem == null)
            {
                Logger.Error("GetRecommendedCategories >> Cannot GetRecommendedGiftsFromRecommendationSystem >> " + input.MemberCode);
                return new PagedResultDto<GetGiftCategoryForView>(
                    0,
                    new List<GetGiftCategoryForView>()
                );
            }
            var now = DateTime.UtcNow;
            // Now query the database
            var listCategoriesCode = getFromRecommendationSystem.data.GiftCategories
                .OrderBy(x => x.GiftCategoryOrder)
                .Select(x => x.GiftCategoryCode)
                .ToList();
            // Nếu listCategoriesCode bị rỗng, thì trả về empty list luôn 
            if (listCategoriesCode.Count == 0)
            {
                Logger.Error("GetRecommendedCategories >> ListCategoriesCode Is Null >> " + input.MemberCode);
                return new PagedResultDto<GetGiftCategoryForView>(
                    0,
                    new List<GetGiftCategoryForView>()
                );
            }
            // Make sure those category will be whitelist for the channel
            var channelInfo = await _giftCategoryChannelRepository.GetAll()
                .Where(x => x.Code.ToUpper() == "LINKID").AsNoTracking().FirstOrDefaultAsync();
            if (channelInfo == null)
            {
                throw new UserFriendlyException((int)ErrorCodeList.GiftCategoryChannelNotAvailable, langMutil("GiftCategoryChannelNotAvailable"));
            }
            var listAllowedCateCode = await _giftCategoryRepository.GetAll().Where(x => x.IsDeleted == false && x.TenantId == tenantId && x.Channel != null)
                .Where(x => x.Channel.Contains(";" + channelInfo.Id + ";"))
                .AsNoTracking().Select(x => x.Code).ToListAsync();
            listCategoriesCode = listCategoriesCode.Where(x => listAllowedCateCode.Contains(x)).ToList();
            if (listCategoriesCode.Count == 0)
            {
                Logger.Error("GetRecommendedCategories >> ListCategoriesCode Is Null after filter with ChannelID LINKID >> " + input.MemberCode);
                return new PagedResultDto<GetGiftCategoryForView>(
                    0,
                    new List<GetGiftCategoryForView>()
                );
            }
            var filteredGiftCategories = _giftCategoryRepository.GetAll()
                .Where(e => !e.IsDeleted && e.Status == "A" && listCategoriesCode.Contains(e.Code));

            var query = (from o in filteredGiftCategories
                         join o1 in _giftCategoryRepository.GetAll() on o.ParentId equals o1.Id into j1
                         from s1 in j1.DefaultIfEmpty()

                         select new GetGiftCategoryForView()
                         {
                             GiftCategory = ObjectMapper.Map<GiftCategoryDto>(o)
                         ,
                             ParentNameGiftCategory = s1 == null ? "" : s1.Name.ToString(),
                         });

            var totalCount = await query.CountAsync();

            var giftCategories = await query
                .OrderBy("giftCategory.id desc").Skip(0).Take(50)
                .ToListAsync();
            if (giftCategories.Count > 0)
            {
                giftCategories.ForEach(p =>
                {
                    var imageLink = _imageLinkRepository
                            .FirstOrDefault(x => x.Code == p.GiftCategory.Code && x.Type == "GiftCategory");
                    p.GiftCategory.ImageLink = ObjectMapper.Map<ImageLinkDto>(imageLink);
                });
            }
            // Sort giftCategories theo order ben Data truyen sang
            giftCategories = giftCategories.OrderBy(x => listCategoriesCode.IndexOf(x.GiftCategory.Code)).ToList();

            var retValue = new PagedResultDto<GetGiftCategoryForView>(
                totalCount,
                giftCategories
            );
            await _cache.SetStringAsync(cacheKey, JsonConvert.SerializeObject(retValue), new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromHours(3)));
            return retValue;
        }

        [AbpAuthorize(AppPermissions.Pages_GiftCategory_Delete)]
        public async Task DeleteCustomAsync(EntityDto input)
        {
            //get child node
            InitListGiftCategory();
            var parentItem = listGiftCategory.FirstOrDefault(p => p.Id == input.Id);
            var childNode = getChildItem(input.Id);
            childNode.Add(parentItem);

            childNode.ForEach(async p =>
            {
                await _giftCategoryRepository.DeleteAsync(p.Id);
            });
            List<string> codesGiftCategory = childNode.Select(p => p.Code).ToList();
            await DeleteListFromCategory(codesGiftCategory);


        }

        public async Task<GiftCategoryDto> CreateOrEditReturnMulti(ThirdPartyCreateOrEditGiftCategoryDto input)
        {
            var result = new GiftCategoryDto();
            if (input.IsCreate)
            {
                foreach (var category in input.Categories)
                {
                    var giftCategoryMapping = await _giftCategoryRepository.FirstOrDefaultAsync(x => x.ThirdPartyGiftCategoryId == category.ThirdPartyGiftCategoryId && x.VendorId == category.VendorId);
                    if (giftCategoryMapping != null)
                    {
                        result = await Update(category, true);
                    }
                    else
                    {
                        result = await Create(category);
                    }

                }
            }
            else
            {
                foreach (var category in input.Categories)
                {
                    if (category.Id.HasValue)
                    {
                        if (category.IsDeleteCategory.HasValue && category.IsDeleteCategory.Value == true)
                        {
                            await DeleteListGiftThirdParty(new EntityDto { Id = category.Id.Value });
                        }
                        else
                        {
                            result = await Update(category);
                        }
                    }
                    else
                    {
                        result = await Create(category);
                    }
                }
            }
            return result;
        }

        public async Task<GiftCategoryDto> CreateOrEditReturn(CreateOrEditGiftCategoryDto input)
        {
            if (!string.IsNullOrEmpty(input.Channel))
            {
                input.Channel = input.Channel.Substring(0, 1) == ";" ? input.Channel : ";" + input.Channel;
                input.Channel = input.Channel.Substring(input.Channel.Length - 1, 1) == ";" ? input.Channel : input.Channel + ";";
            }
            var result = new GiftCategoryDto();
            var checkCategoryNameExist = await _giftCategoryRepository
                .GetAll()
                .Where(x => x.Name == input.Name)
                .Select(x => new
                {
                    Id = x.Id,
                    Name = x.Name
                }).FirstOrDefaultAsync();
            if (input.Id == null)
            {
                if (checkCategoryNameExist != null)
                {
                    throw new UserFriendlyException(langMutil("CategoryNameExist"));
                }
                result = await Create(input);
            }
            else
            {
                if (checkCategoryNameExist != null && checkCategoryNameExist.Id != input.Id)
                {
                    throw new UserFriendlyException(langMutil("CategoryNameExist"));
                }
                result = await Update(input);
            }
            return result;
        }
        /**
         * Cloned from GetAllByMemberCode
         * Ban đầu dùng cho OPES API gọi. Hàm này support filter để get ONLY hidden categories.
         */
        public async Task<PagedResultDto<GetGiftCategoryForView>> GetAllWithoutMemberCode(GetAllWithoutMemberCodeInput input)
        {

            long memoryBeforeFalse = GC.GetTotalMemory(false);
            Logger.Info($"GetAllWithoutMemberCode___1 (without GC): {memoryBeforeFalse / (1024.0 * 1024.0)} MB");

            var channelInfo = await _giftCategoryChannelRepository.GetAll()
                .Where(x => x.Code.ToUpper() == input.PartnerCode.Trim().ToUpper()).AsNoTracking().FirstOrDefaultAsync();
            if (channelInfo == null)
            {
                throw new UserFriendlyException((int)ErrorCodeList.GiftCategoryChannelNotAvailable, langMutil("GiftCategoryChannelNotAvailable"));
            }
            var isFilterHiddenCategories = input.OnlyHidden ?? false;

            var diamondParent = await _giftCategoryRepository.GetAll()
                .Where(x => x.Code == "149d7e5d-1cc5-4970-9859-28acf80ba047" || x.Code == "2dc78fd4-c3ae-4cda-8dd5-ebcd5bd79465").FirstOrDefaultAsync();
            var diamondParentCategory = "";
            if (diamondParent != null)
            {
                diamondParentCategory = diamondParent.Code;
            }


            var listExclude = new List<string>
            {
                "c3f8b851-2fc0-418a-bf22-3234ce4cdbd7",
                "81044caf-eddb-40ee-a7c7-b862be9818e0",
                "84afa499-7e53-4cbc-b442-2286ee7ff545",
                diamondParentCategory
            };

            var filteredGiftCategories = _giftCategoryRepository.GetAll()
                        .WhereIf(!string.IsNullOrWhiteSpace(input.Filter), e => false || e.Code.Contains(input.Filter) || e.Name.Contains(input.Filter) || e.Description.Contains(input.Filter) || e.Status.Contains(input.Filter) || e.ParentCode.Contains(input.Filter)
                            || (e.Is3rdPartyGiftCategory && (e.Parent.Name.Contains(input.Filter) || e.Vendor.VendorName.Contains(input.Filter))))
                        .WhereIf(!string.IsNullOrWhiteSpace(input.CodeFilter), e => e.Code.ToLower().Contains(input.CodeFilter.ToLower().Trim()))
                        .WhereIf(!string.IsNullOrWhiteSpace(input.NameFilter), e => e.Name.ToLower().Contains(input.NameFilter.ToLower().Trim()))
                        .WhereIf(!string.IsNullOrWhiteSpace(input.DescriptionFilter), e => e.Description.ToLower() == input.DescriptionFilter.ToLower().Trim())
                        .WhereIf(!string.IsNullOrWhiteSpace(input.StatusFilter), e => e.Status.ToLower() == input.StatusFilter.ToLower().Trim())
                        .WhereIf(!string.IsNullOrWhiteSpace(input.ParentName), e => e.Is3rdPartyGiftCategory && e.Parent.Name.ToLower().Contains(input.ParentName.ToLower().Trim()))
                        .WhereIf(!string.IsNullOrWhiteSpace(input.VendorName), e => e.Is3rdPartyGiftCategory && e.Vendor.VendorName.ToLower().Contains(input.VendorName.ToLower().Trim()))
                        .WhereIf(input.MinLevelFilter != null, e => e.Level >= input.MinLevelFilter)
                        .WhereIf(input.MaxLevelFilter != null, e => e.Level <= input.MaxLevelFilter)
                        .WhereIf(!string.IsNullOrWhiteSpace(input.ParentCodeFilter), e => e.ParentCode.ToLower() == input.ParentCodeFilter.ToLower().Trim())
                        .WhereIf(input.Is3rdPartyGiftCategory.HasValue, e => e.Is3rdPartyGiftCategory == input.Is3rdPartyGiftCategory)
                        .Where(x => x.Channel != null && x.Channel.Contains(";" + channelInfo.Id + ";"))
                        // CODE phải khác DIAMOND, và PARENT cũng phải khác DIAMOND
                        .Where(x => x.ParentCode != diamondParentCategory) // Prod parent diamond
                        .Where(x => !listExclude.Contains(x.Code)) // Prod parent diamond
                        .Where(e => e.IsHidden == isFilterHiddenCategories)
                        .OrderBy(input.Sorting ?? "id DESC");


            var query = (from o in filteredGiftCategories
                         join o1 in _giftCategoryRepository.GetAll() on o.ParentId equals o1.Id into j1
                         from s1 in j1.DefaultIfEmpty()

                         select new GetGiftCategoryForView()
                         {
                             GiftCategory = ObjectMapper.Map<GiftCategoryDto>(o)
                         ,
                             ParentNameGiftCategory = s1 == null ? "" : s1.Name.ToString()

                         })

                        .WhereIf(!string.IsNullOrWhiteSpace(input.ParentNameGiftCategoryFilter), e => e.ParentNameGiftCategory.ToLower() == input.ParentNameGiftCategoryFilter.ToLower().Trim());

            var totalCount = await query.CountAsync();

            var giftCategories = new List<GetGiftCategoryForView>();
            giftCategories = await query
               .Skip((int)input.SkipCount).Take((int)input.MaxResultCount)
               .ToListAsync();

            if (giftCategories.Count > 0)
            {
                var vendorIds = giftCategories.Where(t => t.GiftCategory.Is3rdPartyGiftCategory).Select(t => t.GiftCategory.VendorId).ToList();
                var vendors = _thirdPartyGiftVendorRepository.GetAll().Where(t => vendorIds.Contains(t.Id)).AsNoTracking().ToList();

                var giftCategoryCodes = giftCategories.Select(x => x.GiftCategory.Code).Distinct().ToList();
                var images = await _imageLinkRepository.GetAll()
                    .Where(x => x.Type == "GiftCategory" && giftCategoryCodes.Contains(x.Code))
                    .AsNoTracking().ToListAsync();

                giftCategories.ForEach(p =>
                {
                    var imageLink = images
                            .FirstOrDefault(x => x.Code == p.GiftCategory.Code && x.Type == "GiftCategory");
                    p.GiftCategory.ImageLink = ObjectMapper.Map<ImageLinkDto>(imageLink);
                    p.GiftCategory.VendorName = vendors.FirstOrDefault(t => t.Id == p.GiftCategory.VendorId)?.VendorName;

                    if (p.GiftCategory.LastModifierUserId != null)
                    {
                        p.GiftCategory.LastModifierUserName = GetUserNameByIdsAsync((long)p.GiftCategory.LastModifierUserId).Result;
                    }
                });
            }

            memoryBeforeFalse = GC.GetTotalMemory(false);
            Logger.Info($"GetAllWithoutMemberCode___2 (without GC): {memoryBeforeFalse / (1024.0 * 1024.0)} MB");

            return new PagedResultDto<GetGiftCategoryForView>(
                totalCount,
                giftCategories
            );
        }
        
        /**
         * Get for homepage of LynkID App
         * Đã có cache ở layer Mobi API
         */
        [AbpAuthorize(AppPermissions.Pages_GiftCategory)]
        public async Task<PagedResultDto<GetGiftCategoryForView>> GetAllWithoutMemberCodeForHomePage(GetAllWithoutMemberCodeInput input)
        {

            var totalCount = 0;
            var listCategoriesAndInfors = new List<GetGiftCategoryForView>();

            Logger.Info("[GetAllWithoutMemberCodeForHomePage] Get From DB");
            var queryFilter = string.Empty;
            var channelFilter = string.Empty;
            var queryMinLevelFilter = string.Empty;
            var queryMaxLevelFilter = string.Empty;
            var queryParentCodeFilter = string.Empty;
            var queryOrderBy = input.Sorting != null ? " ORDER BY " + FillterUltil.SortFillter(input.Sorting) + ", e.DisplayOrder ASC , e.LastModificationTime DESC, e.Id desc " : " ORDER BY e.DisplayOrder ASC, e.LastModificationTime DESC, e.Id desc ";
            var queryLimitMaxRecord = string.Format(" LIMIT {0} OFFSET {1}", input.MaxResultCount, input.SkipCount);
            var tenantId = AbpSession.TenantId ?? 0;
            if (!string.IsNullOrWhiteSpace(input.Filter))
            {
                queryFilter = string.Format(" AND " +
                    "(LOCATE('{0}', `e`.`Code`) > 0 " +
                    "OR LOCATE('{0}', `e`.`Name`) > 0 " +
                    "OR LOCATE('{0}', `e`.`Description`) > 0 " +
                    "OR LOCATE('{0}', `e`.`Status`) > 0 " +
                    "OR LOCATE('{0}', `e`.`ParentCode`) > 0)", input.Filter);
            }
            if (input.MinLevelFilter != null)
            {
                queryMinLevelFilter = string.Format(" AND `e`.`Level` >= {0}", input.MinLevelFilter);
            }
            if (input.MaxLevelFilter != null)
            {
                queryMaxLevelFilter = string.Format(" AND `e`.`Level` <= {0}", input.MaxLevelFilter);
            }
            if (!string.IsNullOrWhiteSpace(input.ParentCodeFilter))
            {
                queryParentCodeFilter = string.Format(" AND `e`.`ParentCode` = '{0}'", input.ParentCodeFilter);
            }
            string channel = "LINKID";
            if (!string.IsNullOrWhiteSpace(input.ConnectSource) && input.ConnectSource == "Webapp")
            {
                channel = "LynkiDWebApp";
            }    
            var channelInfo = await _giftCategoryChannelRepository.GetAll()
                            .Where(x => x.Code.ToUpper() == channel).AsNoTracking().FirstOrDefaultAsync();
            if (channelInfo == null)
            {
                throw new UserFriendlyException((int)ErrorCodeList.GiftCategoryChannelNotAvailable, langMutil("GiftCategoryChannelNotAvailable"));
            }

            channelFilter = $" AND `e`.`Channel` LIKE '%;{channelInfo.Id};%' ";
            //string queryCateHidden = string.Empty;
            //if (!string.IsNullOrWhiteSpace(input.ConnectSource) && input.ConnectSource == "Webapp")
            //{
            //    //trong trường hợp source connect là webapp
            //    var cateHiddenCacheKey = $"GiftCategoryHiddenWithWebApp_{AbpSession.TenantId}";
            //    var cateHiddenstr = await _cache.GetStringAsync(cateHiddenCacheKey);
            //    if (!string.IsNullOrWhiteSpace(cateHiddenstr))
            //    {
            //        queryCateHidden = $" AND (`e`.`Code` NOT IN ({cateHiddenstr}) AND (`e`.`ParentCode` IS NULL OR `e`.`ParentCode` NOT IN ({cateHiddenstr})) ) AND `e`.`Name` IS not null AND `e`.`Name` <> '' AND `e`.`Level` < 2 ";
            //    }
            //    else
            //    {
            //        var cateHiddlen = await _settingsRepository.GetAll().AsNoTracking()
            //                                .Where(x => x.TenantId == AbpSession.TenantId && x.Name == "Loyalty.CateHiddenWithWebApp")
            //                                .FirstOrDefaultAsync();
            //        if(cateHiddlen != null)
            //        {
            //            queryCateHidden = $" AND ( `e`.`Code` NOT IN ({cateHiddlen.Value}) AND (`e`.`ParentCode` IS NULL OR `e`.`ParentCode` NOT IN ({cateHiddlen.Value})) ) AND `e`.`Name` IS not null AND `e`.`Name` <> '' AND `e`.`Level` < 2  ";
            //            await _cache.SetStringAsync($"GiftCategoryHiddenWithWebApp_{AbpSession.TenantId}", cateHiddlen.Value, new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromHours(1)));
            //        }
            //    }
            //}
            var connectionString = _appConfigurationAccessor.Configuration[$"ConnectionStrings:{SaaSConsts.ConnectionStringName}"];
            var parameters = new Dictionary<string, object>
            {
                { "@TenantId",  tenantId },
            };
            var sqlString =
                @"SELECT SQL_CALC_FOUND_ROWS `e`.`Code`, `e`.`Name`, `e`.`ParentCode`, `e`.`ParentId`, `e`.`Description`, `t`.*
                    FROM `GiftCategory` AS `e`
						LEFT JOIN (SELECT `x`.`Id`, `x`.`Code` AS CodeImageLink, `x`.`FullLink`, `x`.`isActive`
								FROM `ImageLink` AS `x`
								WHERE `x`.`IsDeleted` = false AND `x`.`TenantId` = @TenantId AND `x`.`IsActive` = TRUE
						) AS `t` ON `e`.`Code` = `t`.`CodeImageLink`
                    WHERE `e`.`IsDeleted` = false AND `e`.`TenantId` = @TenantId AND `e`.`Status` = 'A' AND `e`.`Name` is not null and `e`.`IsHidden` = false and `e`.`Level` = 1 and `e`.`Name` is not null "
                + channelFilter
                + queryFilter
                + queryMinLevelFilter
                + queryMaxLevelFilter
                + queryParentCodeFilter
                //+ queryCateHidden
                + queryOrderBy
                + queryLimitMaxRecord
                + "; SELECT FOUND_ROWS();";

            var giftCategories = _sqlExecuteManager.QueryListAndCount<GiftCategoryAndInfoModel>(connectionString,
                sqlString
                        , parameters);
            totalCount = giftCategories.Count;
            if (giftCategories.obj.Count() > 0)
            {

                foreach (var itemCategories in giftCategories.obj)
                {
                    var result = new GetGiftCategoryForView();
                    var md = new GiftCategoryDto()
                    {
                        Code = itemCategories.Code,
                        Name = itemCategories.Name, ParentCode = itemCategories.ParentCode, ParentId = itemCategories.ParentId, 
                    };
                    result.GiftCategory = md;
                    if (itemCategories.Id != null)
                    {
                        result.GiftCategory.ImageLink = new ImageLinkDto()
                        {
                            FullLink = itemCategories.FullLink, Link = itemCategories.FullLink
                        };
                    }
                    listCategoriesAndInfors.Add(result);
                }
            }
            return new PagedResultDto<GetGiftCategoryForView>(
                totalCount,
                listCategoriesAndInfors
            );
        }

        [AbpAuthorize(AppPermissions.Pages_GiftCategory)]
        public async Task<PagedResultDto<GetGiftCategoryForView>> GetAllByMemberCode(GetAllByMemberCodeGiftCategoriesInput input)
        {
            Logger.Info($"GetAllByMemberCode___Input:{JsonConvert.SerializeObject(input)}");
            if (string.IsNullOrEmpty(input.Channel))
            {
                input.Channel = "vpbank";
            }
            var giftCategoryCache = await _cache.GetStringAsync($"_GetGiftCategoryForView_giftCategories_{input.Channel}");
            var totalCount = 0;
            var giftCategories = new List<GetGiftCategoryForView>();

            if (string.IsNullOrEmpty(giftCategoryCache))
            {
                Logger.Info("[GetAllByMemberCode] Get From DB");

                GiftCategoryChannel channelInfo = null;
                channelInfo = await _giftCategoryChannelRepository.GetAll()
                                    .Where(x => x.Code.ToUpper() == input.Channel.Trim().ToUpper()).FirstOrDefaultAsync();

                var listTargetIdByMember = _targetAudienceCommonAppService.CheckMemberCodeandGetAllTargetAudience(input.MemberCode, AbpSession.TenantId ?? 0);
                var filteredGiftCategories = _giftCategoryRepository.GetAll()
                            .WhereIf(!string.IsNullOrWhiteSpace(input.Filter), e => false || e.Code.Contains(input.Filter) || e.Name.Contains(input.Filter) || e.Description.Contains(input.Filter) || e.Status.Contains(input.Filter) || e.ParentCode.Contains(input.Filter)
                                || (e.Is3rdPartyGiftCategory && (e.Parent.Name.Contains(input.Filter) || e.Vendor.VendorName.Contains(input.Filter))))
                            .WhereIf(!string.IsNullOrWhiteSpace(input.CodeFilter), e => e.Code.ToLower().Contains(input.CodeFilter.ToLower().Trim()))
                            .WhereIf(!string.IsNullOrWhiteSpace(input.NameFilter), e => e.Name.ToLower().Contains(input.NameFilter.ToLower().Trim()))
                            .WhereIf(!string.IsNullOrWhiteSpace(input.DescriptionFilter), e => e.Description.ToLower() == input.DescriptionFilter.ToLower().Trim())
                            .WhereIf(!string.IsNullOrWhiteSpace(input.StatusFilter), e => e.Status.ToLower() == input.StatusFilter.ToLower().Trim())
                            .WhereIf(!string.IsNullOrWhiteSpace(input.ParentName), e => e.Is3rdPartyGiftCategory && e.Parent.Name.ToLower().Contains(input.ParentName.ToLower().Trim()))
                            .WhereIf(!string.IsNullOrWhiteSpace(input.VendorName), e => e.Is3rdPartyGiftCategory && e.Vendor.VendorName.ToLower().Contains(input.VendorName.ToLower().Trim()))
                            .WhereIf(input.MinLevelFilter != null, e => e.Level >= input.MinLevelFilter)
                            .WhereIf(input.MaxLevelFilter != null, e => e.Level <= input.MaxLevelFilter)
                            .WhereIf(!string.IsNullOrWhiteSpace(input.ParentCodeFilter), e => e.ParentCode.ToLower() == input.ParentCodeFilter.ToLower().Trim())
                            .WhereIf(input.Is3rdPartyGiftCategory.HasValue, e => e.Is3rdPartyGiftCategory == input.Is3rdPartyGiftCategory)
                            .Where(e => e.TargetAudienceId == null || listTargetIdByMember.Contains(e.TargetAudienceId.Value))
                            .Where(e => !e.IsHidden)
                            .WhereIf(channelInfo != null , e => e.Channel.Contains(string.Format(";{0};", channelInfo.Id)))
                            .OrderBy(input.Sorting ?? "id DESC");


                var query = (from o in filteredGiftCategories
                             join o1 in _giftCategoryRepository.GetAll() on o.ParentId equals o1.Id into j1
                             from s1 in j1.DefaultIfEmpty()

                             select new GetGiftCategoryForView()
                             {
                                 GiftCategory = ObjectMapper.Map<GiftCategoryDto>(o)
                             ,
                                 ParentNameGiftCategory = s1 == null ? "" : s1.Name.ToString()

                             })

                            .WhereIf(!string.IsNullOrWhiteSpace(input.ParentNameGiftCategoryFilter), e => e.ParentNameGiftCategory.ToLower() == input.ParentNameGiftCategoryFilter.ToLower().Trim());

                totalCount = await query.CountAsync();

                giftCategories = await query
                   .Skip((int)input.SkipCount).Take((int)input.MaxResultCount)
                   .ToListAsync();
                //if (input.MaxResultCount !=null)
                //{
                //    giftCategories = await query
                //    .OrderBy(input.Sorting ?? "giftCategory.id asc")
                //    .Skip((int)input.SkipCount).Take((int)input.MaxResultCount)
                //    .ToListAsync();
                //}
                //else
                //{
                //    giftCategories = await query
                //   .OrderBy(input.Sorting ?? "giftCategory.id asc")
                //   .Skip((int)input.SkipCount)
                //   .ToListAsync();
                //}

                if (giftCategories.Count > 0)
                {
                    var vendorIds = giftCategories.Where(t => t.GiftCategory.Is3rdPartyGiftCategory).Select(t => t.GiftCategory.VendorId).ToList();
                    var vendors = _thirdPartyGiftVendorRepository.GetAll().Where(t => vendorIds.Contains(t.Id)).ToList();
                    giftCategories.ForEach(p =>
                    {
                        var imageLink = _imageLinkRepository
                                .FirstOrDefault(x => x.Code == p.GiftCategory.Code && x.Type == "GiftCategory");
                        p.GiftCategory.ImageLink = ObjectMapper.Map<ImageLinkDto>(imageLink);
                        p.GiftCategory.VendorName = vendors.FirstOrDefault(t => t.Id == p.GiftCategory.VendorId)?.VendorName;

                        if (p.GiftCategory.LastModifierUserId != null)
                        {
                            p.GiftCategory.LastModifierUserName = GetUserNameByIdsAsync((long)p.GiftCategory.LastModifierUserId).Result;
                        }
                    });
                    await _cache.SetStringAsync($"_GetGiftCategoryForView_giftCategories_{input.Channel}", JsonConvert.SerializeObject(giftCategories),
                        new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromHours(12)));
                    await _cache.SetStringAsync($"_GetGiftCategoryForView_totalCount_{input.Channel}", totalCount.ToString(),
                       new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromHours(12)));
                }
            }
            else
            {
                Logger.Info("[GetAllByMemberCode] Get From Cache");
                giftCategories = JsonConvert.DeserializeObject<List<GetGiftCategoryForView>>(giftCategoryCache);
                var totalCountCache = await _cache.GetStringAsync($"_GetGiftCategoryForView_totalCount_{input.Channel}");
                totalCount = int.Parse(totalCountCache);
            }

            return new PagedResultDto<GetGiftCategoryForView>(
                totalCount,
                giftCategories
            );
        }

        public async Task<PagedResultDto<GetGiftCategoryForView>> GetAllByMemberCode_v1(GetAllByMemberCodeGiftCategoriesInput input)
        {
            var keyObject = "_GetGiftCategoryForView_giftCategories_NEO";
            var keyCount = "_GetGiftCategoryForView_totalCount_NEO";
            var giftCategoryCache = await _cache.GetStringAsync(keyObject);
            var totalCount = 0;
            var giftCategories = new List<GetGiftCategoryForView>();

            if (string.IsNullOrEmpty(giftCategoryCache))
            {
                Logger.Info("[GetAllByMemberCode] Get From DB");
                var listTargetIdByMember = _targetAudienceCommonAppService.CheckMemberCodeandGetAllTargetAudience(input.MemberCode, AbpSession.TenantId ?? 0);
                var channelInfo = await _giftCategoryChannelRepository.GetAll()
                                .Where(x => x.Code.ToUpper() == input.Channel.Trim().ToUpper()).FirstOrDefaultAsync();
                if (channelInfo == null)
                {
                    throw new UserFriendlyException((int)ErrorCodeList.GiftCategoryChannelNotAvailable, langMutil("GiftCategoryChannelNotAvailable"));
                }
                var filteredGiftCategories = _giftCategoryRepository.GetAll()
                            .WhereIf(!string.IsNullOrWhiteSpace(input.Filter), e => false || e.Code.Contains(input.Filter) || e.Name.Contains(input.Filter) || e.Description.Contains(input.Filter) || e.Status.Contains(input.Filter) || e.ParentCode.Contains(input.Filter)
                                || (e.Is3rdPartyGiftCategory && (e.Parent.Name.Contains(input.Filter) || e.Vendor.VendorName.Contains(input.Filter))))
                            .WhereIf(!string.IsNullOrWhiteSpace(input.CodeFilter), e => e.Code.ToLower().Contains(input.CodeFilter.ToLower().Trim()))
                            .WhereIf(!string.IsNullOrWhiteSpace(input.NameFilter), e => e.Name.ToLower().Contains(input.NameFilter.ToLower().Trim()))
                            .WhereIf(!string.IsNullOrWhiteSpace(input.DescriptionFilter), e => e.Description.ToLower() == input.DescriptionFilter.ToLower().Trim())
                            .WhereIf(!string.IsNullOrWhiteSpace(input.StatusFilter), e => e.Status.ToLower() == input.StatusFilter.ToLower().Trim())
                            .WhereIf(!string.IsNullOrWhiteSpace(input.ParentName), e => e.Is3rdPartyGiftCategory && e.Parent.Name.ToLower().Contains(input.ParentName.ToLower().Trim()))
                            .WhereIf(!string.IsNullOrWhiteSpace(input.VendorName), e => e.Is3rdPartyGiftCategory && e.Vendor.VendorName.ToLower().Contains(input.VendorName.ToLower().Trim()))
                            .WhereIf(input.MinLevelFilter != null, e => e.Level >= input.MinLevelFilter)
                            .WhereIf(input.MaxLevelFilter != null, e => e.Level <= input.MaxLevelFilter)
                            .WhereIf(!string.IsNullOrWhiteSpace(input.ParentCodeFilter), e => e.ParentCode.ToLower() == input.ParentCodeFilter.ToLower().Trim())
                            .WhereIf(input.Is3rdPartyGiftCategory.HasValue, e => e.Is3rdPartyGiftCategory == input.Is3rdPartyGiftCategory)
                            .Where(e => e.TargetAudienceId == null || listTargetIdByMember.Contains(e.TargetAudienceId.Value))
                            .Where(x => !x.IsHidden)
                            .Where(x => x.Channel.Contains(string.Format(";{0};", channelInfo.Id)))
                            .OrderBy(input.Sorting ?? "DisplayOrder ASC, LastModificationTime DESC");


                var query = (from o in filteredGiftCategories
                             join o1 in _giftCategoryRepository.GetAll() on o.ParentId equals o1.Id into j1
                             from s1 in j1.DefaultIfEmpty()

                             select new GetGiftCategoryForView()
                             {
                                 GiftCategory = ObjectMapper.Map<GiftCategoryDto>(o)
                             ,
                                 ParentNameGiftCategory = s1 == null ? "" : s1.Name.ToString()

                             })

                            .WhereIf(!string.IsNullOrWhiteSpace(input.ParentNameGiftCategoryFilter), e => e.ParentNameGiftCategory.ToLower() == input.ParentNameGiftCategoryFilter.ToLower().Trim());

                totalCount = await query.CountAsync();

                giftCategories = await query
                   .Skip((int)input.SkipCount).Take((int)input.MaxResultCount)
                   .ToListAsync();

                if (giftCategories.Count > 0)
                {
                    var vendorIds = giftCategories.Where(t => t.GiftCategory.Is3rdPartyGiftCategory).Select(t => t.GiftCategory.VendorId).ToList();
                    var vendors = _thirdPartyGiftVendorRepository.GetAll().Where(t => vendorIds.Contains(t.Id)).ToList();
                    giftCategories.ForEach(p =>
                    {
                        var imageLink = _imageLinkRepository
                                .FirstOrDefault(x => x.Code == p.GiftCategory.Code && x.Type == "GiftCategory");
                        p.GiftCategory.ImageLink = ObjectMapper.Map<ImageLinkDto>(imageLink);
                        p.GiftCategory.VendorName = vendors.FirstOrDefault(t => t.Id == p.GiftCategory.VendorId)?.VendorName;

                        if (p.GiftCategory.LastModifierUserId != null)
                        {
                            p.GiftCategory.LastModifierUserName = GetUserNameByIdsAsync((long)p.GiftCategory.LastModifierUserId).Result;
                        }
                    });
                    Logger.Info(" LinKID Cache GetAllByMemberCode_v1 >> Start ");
                    var cacheOption = new DistributedCacheEntryOptions()
                    {
                        AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5)
                    };
                    await _cache.SetStringAsync(keyObject, JsonConvert.SerializeObject(giftCategories), cacheOption);
                    await _cache.SetStringAsync(keyCount, totalCount.ToString(), cacheOption);
                }
            }
            else
            {
                Logger.Info("[GetAllByMemberCode] Get From Cache");
                giftCategories = JsonConvert.DeserializeObject<List<GetGiftCategoryForView>>(giftCategoryCache);
                var totalCountCache = await _cache.GetStringAsync(keyCount);
                totalCount = int.Parse(totalCountCache);
            }

            return new PagedResultDto<GetGiftCategoryForView>(
                totalCount,
                giftCategories
            );
        }

        [AbpAuthorize(AppPermissions.Pages_GiftCategory_Create)]
        private async Task<GiftCategoryDto> Create(CreateOrEditGiftCategoryDto input)
        {
            if (input.Status.Equals("A") && input.Is3rdPartyGiftCategory)
            {
                var giftExistCategory = _giftCategoryRepository.GetAll().Include(e => e.Vendor)
                        .Where(e => !string.IsNullOrEmpty(input.ThirdPartyGiftCategoryId) && e.Id != input.Id && e.ThirdPartyGiftCategoryId == input.ThirdPartyGiftCategoryId && e.VendorId == input.VendorId).FirstOrDefault();
                var thirdPartyGiftCategory = _integrationCategoryMapping.FirstOrDefault(x => x.IntegrationId == input.ThirdPartyGiftCategoryId && x.VendorId == input.VendorId);
                var thirdPartyGiftCategoryName = thirdPartyGiftCategory != null ? thirdPartyGiftCategory.Title : null;
                if (giftExistCategory != null && (giftExistCategory.ParentCode != null || giftExistCategory.ParentId != null))
                {
                    throw new UserFriendlyException($"Vendor name \"{giftExistCategory.Vendor.VendorName}\" with third-party gift category name \"{thirdPartyGiftCategoryName}\" {langMutil("IsAlreadyExist")}");
                }

                var giftExistBrand = _giftCategoryRepository.GetAll().Include(e => e.Vendor)
                        .Where(e => !string.IsNullOrEmpty(input.ThirdPartyGiftBrandId) && e.Id != input.Id && e.ThirdPartyGiftBrandId == input.ThirdPartyGiftBrandId && e.VendorId == input.VendorId).FirstOrDefault();
                var thirdPartyBrandMapping = _thirdPartyBrandMapping.FirstOrDefault(x => x.ThirdPartyBrandId == input.ThirdPartyGiftBrandId && x.VendorId == input.VendorId);
                var thirdPartyBrandName = thirdPartyBrandMapping != null ? thirdPartyBrandMapping.ThirdPartyBrandName : null;
                if (giftExistBrand != null)
                {
                    throw new UserFriendlyException($"Vendor name \"{giftExistBrand.Vendor.VendorName}\" with third-party gift brand name \"{thirdPartyBrandName}\" {langMutil("IsAlreadyExist")}");
                }
            }

            input.Code = Guid.NewGuid().ToString();

            ImageLink imageLink = new ImageLink();
            if (input.ParentId != null)
            {
                var parentGiftCategory = await _giftCategoryRepository.FirstOrDefaultAsync((int)input.ParentId);
                input.ParentCode = parentGiftCategory.Code;
            }
            var giftCategory = ObjectMapper.Map<GiftCategory>(input);


            if (AbpSession.TenantId != null)
            {
                giftCategory.TenantId = (int?)AbpSession.TenantId;
                imageLink.TenantId = (int?)AbpSession.TenantId;
            }

            giftCategory.Id = await _giftCategoryRepository.InsertAndGetIdAsync(giftCategory);
            imageLink.Code = input.Code;
            imageLink.Type = "GiftCategory";
            if (!string.IsNullOrEmpty(input.ImageLink))
            {
                imageLink.FullLink = input.ImageLink;
                imageLink.Link = input.ImageLink;

            }
            imageLink.Ordinal = 1;
            imageLink.isActive = true;
            await _imageLinkRepository.InsertAsync(imageLink);
            var settingParamId = CreateSettingParam(input);
            giftCategory.TargetAudienceId = settingParamId;
            return ObjectMapper.Map<GiftCategoryDto>(giftCategory);
        }
        private int? CreateSettingParam(CreateOrEditGiftCategoryDto settingInput, bool gencode = true)
        {
            if (settingInput.TargetAudience != null && settingInput.TargetAudience.TargetAudienceDetailDtos != null && settingInput.TargetAudience.TargetAudienceDetailDtos.Count > 0)
            {
                var settingParam = ObjectMapper.Map<SettingParam>(settingInput.TargetAudience);
                settingParam.Id = 0;

                if (gencode)
                {
                    settingParam.Code = _codeGeneratorAppService.GenerateCode(typeof(SettingParam));
                }
                if (AbpSession.TenantId != null)
                {
                    settingParam.TenantId = (int?)AbpSession.TenantId;
                }

                var typeCount = settingInput.TargetAudience.TargetAudienceDetailDtos.GroupBy(field => field.Type).Count();
                settingParam.Count = typeCount;
                var newSettingParamId = _settingParamRepository.InsertAndGetId(settingParam);
                settingInput.TargetAudienceId = newSettingParamId;

                foreach (var settingParamField in settingInput.TargetAudience.TargetAudienceDetailDtos)
                {
                    var settingParamEntity = ObjectMapper.Map<SettingParamField>(settingParamField);
                    if (AbpSession.TenantId != null)
                    {
                        settingParamEntity.TenantId = (int?)AbpSession.TenantId;
                    }
                    settingParamEntity.Id = 0;
                    settingParamEntity.TargetAudienceId = newSettingParamId;
                    _settingParamFieldRepository.Insert(ObjectMapper.Map<SettingParamField>(settingParamEntity));
                }
                return newSettingParamId;
            }
            return null;
        }

        private async Task DeleteSettingParam(int? targetAudienceId)
        {
            var settingParam = _settingParamRepository.GetAll()
                                                      .Include(s => s.TargetAudienceDetail)
                                                      .FirstOrDefault(s => s.Id == targetAudienceId);
            if (settingParam != null)
            {
                foreach (var field in settingParam.TargetAudienceDetail)
                {
                    await _settingParamFieldRepository.DeleteAsync(field.Id);
                }

                await _settingParamRepository.DeleteAsync(settingParam.Id);
            }
        }

        [AbpAuthorize(AppPermissions.Pages_GiftCategory_Edit)]
        private async Task<GiftCategoryDto> Update(CreateOrEditGiftCategoryDto input, bool isUpdateGiftCategory = false)
        {
            if (input.Status.Equals("A") && input.Is3rdPartyGiftCategory)
            {
                var giftExistCategory = _giftCategoryRepository.GetAll().Include(e => e.Vendor)
                        .Where(e => !string.IsNullOrEmpty(input.ThirdPartyGiftCategoryId) && e.Id != input.Id && e.ThirdPartyGiftCategoryId == input.ThirdPartyGiftCategoryId && e.VendorId == input.VendorId).FirstOrDefault();
                var thirdPartyGiftCategory = _integrationCategoryMapping.FirstOrDefault(x => x.IntegrationId == input.ThirdPartyGiftCategoryId && x.VendorId == input.VendorId);
                var thirdPartyGiftCategoryName = thirdPartyGiftCategory != null ? thirdPartyGiftCategory.Title : null;
                if (giftExistCategory != null && (giftExistCategory.ParentCode != null || giftExistCategory.ParentId != null))
                {
                    throw new UserFriendlyException($"Vendor name \"{giftExistCategory.Vendor.VendorName}\" with third-party gift category name \"{thirdPartyGiftCategoryName}\" {langMutil("IsAlreadyExist")}");
                }

                var giftExistBrand = _giftCategoryRepository.GetAll().Include(e => e.Vendor)
                        .Where(e => !string.IsNullOrEmpty(input.ThirdPartyGiftBrandId) && e.Id != input.Id && e.ThirdPartyGiftBrandId == input.ThirdPartyGiftBrandId && e.VendorId == input.VendorId).FirstOrDefault();
                var thirdPartyBrandMapping = _thirdPartyBrandMapping.FirstOrDefault(x => x.ThirdPartyBrandId == input.ThirdPartyGiftBrandId && x.VendorId == input.VendorId);
                var thirdPartyBrandName = thirdPartyBrandMapping != null ? thirdPartyBrandMapping.ThirdPartyBrandName : null;
                if (giftExistBrand != null)
                {
                    throw new UserFriendlyException($"Vendor name \"{giftExistBrand.Vendor.VendorName}\" with third-party gift brand name \"{thirdPartyBrandName}\" {langMutil("IsAlreadyExist")}");
                }
            }

            InitListGiftCategory();
            var giftCategory = new GiftCategory();
            if (isUpdateGiftCategory == true)
            {
                giftCategory = await _giftCategoryRepository.FirstOrDefaultAsync(x => x.ThirdPartyGiftCategoryId == input.ThirdPartyGiftCategoryId
                                                                                            && x.VendorId == input.VendorId);
                input.Id = giftCategory.Id;
            }
            else
            {
                giftCategory = await _giftCategoryRepository.GetAll().FirstOrDefaultAsync(e => e.Id == (int)input.Id);
                input.ParentCode = giftCategory.ParentCode;
            }
            input.Code = giftCategory.Code;

            var imageLink = await _imageLinkRepository.FirstOrDefaultAsync(p => p.Code == giftCategory.Code);
            if (imageLink != null)
            {
                await _imageLinkRepository.DeleteAsync(imageLink.Id);
            }
            //else imageLink = new ImageLink();           
            var newimageLink = new ImageLink();
            if (AbpSession.TenantId != null)
            {
                newimageLink.TenantId = (int?)AbpSession.TenantId;
            }
            newimageLink.Id = 0;
            newimageLink.Code = input.Code;
            newimageLink.Type = "GiftCategory";
            if (!string.IsNullOrEmpty(input.ImageLink))
            {
                newimageLink.FullLink = input.ImageLink;
                newimageLink.Link = input.ImageLink;
            }
            newimageLink.Ordinal = 1;
            newimageLink.isActive = true;
            await _imageLinkRepository.InsertAsync(newimageLink);

            if ((input.Status == "A" && giftCategory.Disable) || (input.Status == "I" && giftCategory.Status == "A"))
            {
                giftCategory.Disable = !giftCategory.Disable;
            }

            //update status child node
            var childNode = getChildItem(giftCategory.Id);
            childNode.ForEach(p =>
            {
                // (input.Status == "A" && p.Disable)
                //if ((input.Status == "I" && p.Status == "A"))
                //{
                //    p.Disable = !p.Disable;
                //    p.Status = input.Status;
                //}
                p.Status = input.Status;
                p.Disable = input.Status.Equals(StatusConst.InActive) ? true : false;

                //Just update Child 3rd gift category with status == Inactive
                if (p.Is3rdPartyGiftCategory && input.Status.Equals(StatusConst.InActive))
                {
                    Update3rdPartyGiftStatus(p.Code, input.Status);
                }
                _giftCategoryRepository.Update(ObjectMapper.Map<GiftCategory>(p));
            });
            var targetAudienceId = giftCategory.TargetAudienceId;
            //Update 3rd gift category status, disable
            if (giftCategory.Is3rdPartyGiftCategory && input.Status.Equals(StatusConst.InActive))
            {
                Update3rdPartyGiftStatus(giftCategory.Code, input.Status);
            }
            // Keep gift list json
            //var giftListJson = giftCategory.GiftListJSON;
            ObjectMapper.Map(input, giftCategory);
            //giftCategory.GiftListJSON = giftListJson;

            await DeleteSettingParam(targetAudienceId);
            var settingParamId = CreateSettingParam(input);
            input.TargetAudienceId = settingParamId;
            giftCategory.TargetAudienceId = settingParamId;

            //// Mapping for tracking
            //var newEntity = new TrackerEntityGiftCategoryInput();
            //newEntity.Code = input.Code;
            //newEntity.Name = input.Name;
            //newEntity.Description = input.Description;
            //newEntity.Status = input.Status;
            //newEntity.Level = input.Level;
            //newEntity.ImageLink = string.IsNullOrEmpty(oldEntity.ImageLink) && string.IsNullOrEmpty(input.ImageLink) ? oldEntity.ImageLink : input.ImageLink;
            //newEntity.CommissPercent = input.CommissPercent;
            //newEntity.Channel = input.Channel;
            //newEntity.CategoryTypeId = input.CategoryTypeId;

            //if (input.TargetAudience != null && input.TargetAudience.TargetAudienceDetailDtos != null && input.TargetAudience.TargetAudienceDetailDtos.Any())
            //{
            //    var detail = input.TargetAudience.TargetAudienceDetailDtos.Select(x => new { x.Type, x.Level, x.Value, x.FullValue, x.SegmentId }).ToList();
            //    newEntity.TargetAudience = JsonConvert.SerializeObject(detail);
            //}
            //_trackerEntityHistoryChangeAppService.HandleTrackerHistory<TrackerEntityGiftCategoryInput>(new CreateTrackerHistoryChangeInput()
            //{
            //    EntityId = giftCategory.Code.ToString(),
            //    ReferId = giftCategory.Id.ToString(),
            //    EntityName = nameof(GiftCategory),
            //    NewEntity = newEntity,
            //    OrginalEntity = oldEntity,
            //    TypeChange = TrackerEntityChangeConsts.Updated
            //});
            //_trackerEntityHistoryChangeAppService.SaveTrackerHistory();

            return ObjectMapper.Map<GiftCategoryDto>(giftCategory);
        }

        public async Task<List<GetGiftCategoryForEditOutput>> GetFetchGiftCategoryForEdit(ThirdPartyGetFetchGiftCategoryForEditInput input)
        {
            var result = new List<GetGiftCategoryForEditOutput>();
            var giftCategories = await this._giftCategoryRepository.GetAll()
                .Where(x => x.ThirdPartyCategoryGroupMapping == input.ThirdPartyCategoryGroupMapping && x.Is3rdPartyGiftCategory)
                .ToListAsync();
            foreach (var category in giftCategories)
            {
                var item = await GetGiftCategoryForEdit(new EntityDto()
                {
                    Id = category.Id
                });
                result.Add(item);
            }
            return result;
        }

        public async Task<List<GetGiftCategoryForEditOutput>> GetFetchGiftCategoryByParentCodeForEdit(ThirdPartyGetFetchMultipleGiftCategoriesForEditInput input)
        {
            var result = new List<GetGiftCategoryForEditOutput>();
            var giftCategories = await this._giftCategoryRepository.GetAll()
                .Where(x => x.ParentCode == input.ParentCategoryCode && x.Is3rdPartyGiftCategory && x.Status == "A")
                .WhereIf(input.VendorId > 0, x => x.VendorId == input.VendorId)
                .WhereIf(input.ThirdPartyCategoryId.IsAny(), x => input.ThirdPartyCategoryId.Contains(x.ThirdPartyGiftCategoryId))
                .WhereIf(input.ThirdPartyBrandId.IsAny(), x => input.ThirdPartyBrandId.Contains(x.ThirdPartyGiftBrandId))
                .ToListAsync();
            foreach (var category in giftCategories)
            {
                var item = await GetMultipleGiftCategories(new EntityDto()
                {
                    Id = category.Id
                }, input);
                result.Add(item);
            }
            return result;
        }

        public class GiftMapping
        {
            public ThirdPartyGiftInfo Gifts { get; set; }
        }

        private CustomizeGiftInfosDto CustomizMappingGifts(GiftInfor gift, IntegrationCategoryMapping integrationCategory, GiftCategory internalCategory, ThirdPartyGiftVendor vendor = null)
        {
            var giftMapped = ObjectMapper.Map<CustomizeGiftInfosDto>(gift);
            giftMapped.Type = gift.ThirdPartyGiftType ?? "";
            giftMapped.Note = gift.Note3rdPartyChange ?? "";
            giftMapped.RequiredCoinOld = gift.RequiredCoinOld;

            giftMapped.DiscountPrice = (gift.DiscountPriceTemp.HasValue) ? gift.DiscountPriceTemp.Value : gift.DiscountPrice;
            giftMapped.RequiredCoin = (gift.RequiredCoinTemp.HasValue) ? gift.RequiredCoinTemp.Value : gift.RequiredCoin;
            giftMapped.SellOutPrice = (gift.SellOutPriceTemp.HasValue) ? gift.SellOutPriceTemp.Value : gift.FullPrice;
            giftMapped.RequiredCoinNew = gift.RequiredCoinNewTemp;
            giftMapped.SellOutPriceNew = gift.SellOutPriceNewTemp;

            if (internalCategory != null)
            {
                giftMapped.InternalCategoryName = internalCategory.Name ?? "";
            }

            if (vendor != null)
            {
                giftMapped.VendorId = vendor.Id;
                giftMapped.VendorName = vendor.VendorName;
                giftMapped.Vendor = gift.Vendor;
                giftMapped.VendorType = vendor.VendorType.ToString();

                if (vendor != null)
                {
                    giftMapped.VendorName = vendor.VendorName ?? "";
                    if (vendor.CoinPriceExchangeRate != 0)
                    {
                        giftMapped.SellInPrice = giftMapped.VendorPrice / (decimal)vendor.CoinPriceExchangeRate;
                        giftMapped.FieldToCal = giftMapped.SellInPrice;
                        //giftMapped.SellOutPrice = gift.RequiredCoin;
                        /* var roundType = GetRoundingType();
                         giftMapped.SellOutPrice = RoundExtensions.RoundByType(giftMapped.SellInPrice * (1 + (decimal)vendor.DefaultMargin / 100), roundType);*/
                    }
                    else
                    {
                        giftMapped.SellInPrice = 0;
                        giftMapped.FieldToCal = 0;
                        giftMapped.SellOutPrice = 0;
                    }

                    if (gift.VendorPriceNew.HasValue)
                    {
                        giftMapped.SellInPriceNew = gift.VendorPriceNew / (decimal)vendor.CoinPriceExchangeRate;
                    }
                }
            }
            return giftMapped;
        }

        [HttpPost]
        public async Task<PagedResultDto<CustomizeGiftInfosView>> Get3rdGifts(ThirdPartGiftGetAllInput input)
        {
            var result = new List<GetGiftCategoryForEditOutput>();
            var totalCount = 0;
            var giftInfors = new List<CustomizeGiftInfosDto>();
            var outputs = new List<CustomizeGiftInfosView>();
            if (input.ParentCategoryCode == "00001")
            {
                var queryGifts = _giftInforRepository.GetAll()
                    .WhereIf(input.VendorId > 0, x => x.VendorId == input.VendorId)
                    .WhereIf(input.VendorId == -1, x => x.Vendor == "LinkId" || x.Vendor == "" || x.Vendor == null)
                    .WhereIf(input.BrandId > 0, x => x.BrandId == input.BrandId)
                    .WhereIf(input.ThirdPartyBrandId != null && input.ThirdPartyBrandId.Count() > 0, x => input.ThirdPartyBrandId.Contains(x.ThirdPartyBrandId))
                    .WhereIf(input.ThirdPartyCategoryId != null && input.ThirdPartyCategoryId.Count() > 0, x => input.ThirdPartyCategoryId.Contains(x.ThirdPartyCategoryId))
                    .WhereIf(!string.IsNullOrEmpty(input.ChangeStatus) && input.ChangeStatus == ChangeStatusConsts.New, x => x.Priority == 5)
                    .WhereIf(!string.IsNullOrEmpty(input.ChangeStatus) && input.ChangeStatus == ChangeStatusConsts.Removed, x => x.Priority == 4)
                    .WhereIf(!string.IsNullOrEmpty(input.ChangeStatus) && input.ChangeStatus == "Price Changed", x => x.Priority == 1)
                    .WhereIf(!string.IsNullOrEmpty(input.ChangeStatus) && input.ChangeStatus == "Effective", x => x.Priority == 2)
                    .WhereIf(!string.IsNullOrEmpty(input.ChangeStatus) && input.ChangeStatus == ChangeStatusConsts.Approved, x => x.Priority == 99)
                    .WhereIf(!string.IsNullOrEmpty(input.Status), x => x.Status == input.Status)
                    .WhereIf(!string.IsNullOrEmpty(input.GiftName), x => x.Name != null && StringUtilHelper.RemoveAccentVietnamese(x.Name.ToLower()).Contains(StringUtilHelper.RemoveAccentVietnamese(input.GiftName.ToLower())))
                    .WhereIf(!string.IsNullOrEmpty(input.GiftCode), x => x.Code.ToLower().Contains(input.GiftCode.ToLower()))
                    .WhereIf(!string.IsNullOrEmpty(input.ThirdPartyGiftCode), x => x.ThirdPartyGiftCode == input.ThirdPartyGiftCode)
                    .WhereIf(input.Is3rdPartyPromotion.HasValue, x => x.Is3rdPartyPromotion == input.Is3rdPartyPromotion);

                // var brands = _thirdPartyBrandMapping.GetAll().Where(x => x.IsActive == true);
                var integrationCat = _integrationCategoryMapping.GetAll().Where(x => x.Status == "A");
                var internalCat = _giftCategoryRepository.GetAll().Where(x => x.Status == "A" && !x.Is3rdPartyGiftCategory);

                var giftsTmp = from g in queryGifts
                               join c in integrationCat on new { IntegrationCategoryId = g.ThirdPartyCategoryId, VendorIdMap = g.VendorId }
                                                    equals new { IntegrationCategoryId = c.IntegrationId, VendorIdMap = c.VendorId } into gc
                               from c in gc.DefaultIfEmpty()
                               join ic in internalCat on g.FullGiftCategoryCode equals ic.Code into gic
                               from ic in gic.DefaultIfEmpty()
                               select new CustomizeGiftInfosView()
                               {
                                   CustomizeGift = CustomizMappingGifts(g, c, ic, null)
                               };

                var totalGiftActive = await giftsTmp.Where(x => x.CustomizeGift.Status == StatusConst.Active).CountAsync();
                var totalGiftInActive = await giftsTmp.Where(x => x.CustomizeGift.Status == StatusConst.InActive).CountAsync();
                var totalChangePrice = await giftsTmp.Where(x => x.CustomizeGift.Priority == 1).CountAsync();
                var totalChangeEffective = await giftsTmp.Where(x => x.CustomizeGift.Priority == 2).CountAsync();
                var totalNoChange = await giftsTmp.Where(x => x.CustomizeGift.Priority == 99 || (x.CustomizeGift.Priority == null)).CountAsync();
                var totalNew = await giftsTmp.Where(x => x.CustomizeGift.Priority == 5).CountAsync();
                var totalRemove = await giftsTmp.Where(x => x.CustomizeGift.Priority == 4).CountAsync();


                totalCount = giftsTmp.Count();
                // giftInfors = giftsTmp.Skip(input.SkipCount).Take(input.MaxResultCount).ToList();
                outputs = await giftsTmp
               .OrderBy(input.Sorting ?? "CustomizeGift.Priority asc")
               .PageBy(input)
               .ToListAsync();

                var vendors = _thirdPartyGiftVendorRepository.GetAll().Where(x => x.Status == "A")
                                    .Select(v => new
                                    {
                                        VendorId = v.Id,
                                        Name = v.VendorName,
                                        CoinPriceExchangeRate = (decimal)v.CoinPriceExchangeRate
                                    }).ToList();
                if (outputs.IsAny())
                {
                    outputs[0].CustomizeGift.TotalActive = totalGiftActive;
                    outputs[0].CustomizeGift.TotalInActive = totalGiftInActive;
                    outputs[0].CustomizeGift.TotalChangePrice = totalChangePrice;
                    outputs[0].CustomizeGift.TotalChangeEffective = totalChangeEffective;
                    outputs[0].CustomizeGift.TotalNoChange = totalNoChange;
                    outputs[0].CustomizeGift.TotalNew = totalNew;
                    outputs[0].CustomizeGift.TotalRemove = totalRemove;
                }

                foreach (var gift in outputs)
                {
                    /*gift.CustomizeGift.TotalActive = totalGiftActive;
                    gift.CustomizeGift.TotalInActive = totalGiftInActive;
                    gift.CustomizeGift.TotalChangePrice = totalChangePrice;
                    gift.CustomizeGift.TotalChangeEffective = totalChangeEffective;
                    gift.CustomizeGift.TotalNoChange = totalNoChange;
                    gift.CustomizeGift.TotalNew = totalNew;
                    gift.CustomizeGift.TotalRemove = totalRemove;*/

                    var vendor = vendors.FirstOrDefault(x => x.VendorId == gift.CustomizeGift.VendorId);
                    if (vendor != null)
                    {
                        gift.CustomizeGift.VendorName = vendor.Name ?? "";

                        if (vendor.CoinPriceExchangeRate != 0)
                        {
                            gift.CustomizeGift.SellInPrice = gift.CustomizeGift.VendorPrice / vendor.CoinPriceExchangeRate;
                            gift.CustomizeGift.FieldToCal = gift.CustomizeGift.SellInPrice;
                        }
                        else
                        {
                            gift.CustomizeGift.SellInPrice = 0;
                            gift.CustomizeGift.FieldToCal = 0;
                        }
                    }

                    var brand = _thirdPartyBrandMapping.FirstOrDefault(x => x.IsActive == true && x.ThirdPartyBrandId == gift.CustomizeGift.ThirdPartyGiftBrandId && x.VendorId == gift.CustomizeGift.VendorId);
                    if (brand != null)
                    {
                        gift.CustomizeGift.ThirdPartyBrandName = brand.ThirdPartyBrandName;
                    }

                    if (gift.CustomizeGift.VendorPriceNew.HasValue)
                    {
                        gift.CustomizeGift.SellInPriceNew = gift.CustomizeGift.VendorPriceNew / vendor.CoinPriceExchangeRate;
                    }

                    if (!string.IsNullOrEmpty(gift.CustomizeGift.ThirdPartyCategoryId))
                    {
                        var integrationCategory = await integrationCat.FirstOrDefaultAsync(x => x.IntegrationId == gift.CustomizeGift.ThirdPartyCategoryId.ToString() && x.VendorId == gift.CustomizeGift.VendorId);
                        if (integrationCategory != null)
                        {
                            gift.CustomizeGift.CategoryName = integrationCategory.Title;
                        }
                    }
                    gift.CustomizeGift.InternalCategoryName = await MapInternalCategoryName(gift.CustomizeGift);
                }

                var roundType = GetRoundingType();


            }
            else
            {
                var queryGifts = _giftInforRepository.GetAll()
                    //.Where(x => x.Selected && x.VendorId.HasValue)
                    .WhereIf(input.BrandId > 0, x => x.BrandId == input.BrandId)
                    .WhereIf(input.VendorId > 0, x => x.VendorId == input.VendorId)
                    .WhereIf(input.VendorId == -1, x => x.Vendor == "LinkId" || x.Vendor == "" || x.Vendor == null)
                    .WhereIf(input.ThirdPartyBrandId != null && input.ThirdPartyBrandId.Count() > 0, x => input.ThirdPartyBrandId.Contains(x.ThirdPartyBrandId))
                    .WhereIf(input.ThirdPartyCategoryId != null && input.ThirdPartyCategoryId.Count() > 0, x => input.ThirdPartyCategoryId.Contains(x.ThirdPartyCategoryId))
                    .WhereIf(!string.IsNullOrEmpty(input.ParentCategoryCode), x => x.FullGiftCategoryCode.ToLower().Contains(input.ParentCategoryCode.ToLower().Trim()))
                    .WhereIf(!string.IsNullOrEmpty(input.ChangeStatus) && input.ChangeStatus == ChangeStatusConsts.New, x => x.Priority == 5)
                    .WhereIf(!string.IsNullOrEmpty(input.ChangeStatus) && input.ChangeStatus == ChangeStatusConsts.Removed, x => x.Priority == 4)
                    .WhereIf(!string.IsNullOrEmpty(input.ChangeStatus) && input.ChangeStatus == "Price Changed", x => x.Priority == 1)
                    .WhereIf(!string.IsNullOrEmpty(input.ChangeStatus) && input.ChangeStatus == "Effective", x => x.Priority == 2)
                    .WhereIf(!string.IsNullOrEmpty(input.ChangeStatus) && input.ChangeStatus == ChangeStatusConsts.Approved, x => x.Priority == 99)
                    .WhereIf(!string.IsNullOrEmpty(input.Status), x => x.Status == input.Status)
                    .WhereIf(!string.IsNullOrEmpty(input.GiftName), x => x.Name != null && StringUtilHelper.RemoveAccentVietnamese(x.Name.ToLower()).Contains(StringUtilHelper.RemoveAccentVietnamese(input.GiftName.ToLower())))
                    .WhereIf(!string.IsNullOrEmpty(input.GiftCode), x => x.Code.ToLower().Contains(input.GiftCode.ToLower()))
                    .WhereIf(!string.IsNullOrEmpty(input.ThirdPartyGiftCode), x => x.ThirdPartyGiftCode == input.ThirdPartyGiftCode)
                    .WhereIf(input.Is3rdPartyPromotion.HasValue, x => x.Is3rdPartyPromotion == input.Is3rdPartyPromotion);

                var integrationCat = _integrationCategoryMapping.GetAll().Where(x => x.Status == "A");
                var internalCat = _giftCategoryRepository.GetAll().Where(x => x.Status == "A" && !x.Is3rdPartyGiftCategory);
                //  .WhereIf(!string.IsNullOrEmpty(input.ParentCategoryCode), x => x.Code == input.ParentCategoryCode);
                var vendors = _thirdPartyGiftVendorRepository.GetAll().Where(x => x.Status == "A");
                // .WhereIf(input.VendorId > 0, x => x.Id == input.VendorId);

                var giftsTmp = from g in queryGifts
                               join c in integrationCat on new { IntegrationCategoryId = g.ThirdPartyCategoryId, VendorIdMap = g.VendorId }
                                                    equals new { IntegrationCategoryId = c.IntegrationId, VendorIdMap = c.VendorId } into gc
                               from c in gc.DefaultIfEmpty()
                               join ic in internalCat on g.FullGiftCategoryCode equals ic.Code into gic
                               from ic in gic.DefaultIfEmpty()
                               join v in vendors on g.VendorId equals v.Id into gv
                               from v in gv.DefaultIfEmpty()
                                   /*join v in vendors on g.VendorId equals v.Id*/
                               select new CustomizeGiftInfosView
                               {
                                   CustomizeGift = CustomizMappingGifts(g, c, ic, v)
                               };

                //Gifts was stored in database
                var oldGifts = giftsTmp.WhereIf(input.VendorId > 0, x => x.CustomizeGift.VendorId > 0).ToList();

                giftsTmp = giftsTmp.WhereIf(input.VendorId > 0, x => x.CustomizeGift.VendorId > 0);

                var totalGiftActive = await giftsTmp.Where(x => x.CustomizeGift.Status == StatusConst.Active).CountAsync();
                var totalGiftInActive = await giftsTmp.Where(x => x.CustomizeGift.Status == StatusConst.InActive).CountAsync();
                var totalChangePrice = await giftsTmp.Where(x => x.CustomizeGift.Priority == 1).CountAsync();
                var totalChangeEffective = await giftsTmp.Where(x => x.CustomizeGift.Priority == 2).CountAsync();
                var totalNoChange = await giftsTmp.Where(x => x.CustomizeGift.Priority == 99 || x.CustomizeGift.Priority == null).CountAsync();
                var totalNew = await giftsTmp.Where(x => x.CustomizeGift.Priority == 5).CountAsync();
                var totalRemove = await giftsTmp.Where(x => x.CustomizeGift.Priority == 4).CountAsync();

                totalCount = await giftsTmp.CountAsync();
                //giftInfors = giftsTmp.Skip(input.SkipCount).Take(input.MaxResultCount).ToList();
                outputs = await giftsTmp
               //.OrderBy(input.Sorting ?? "transaction.businessTime asc")
               .PageBy(input)
               .ToListAsync();

                if (outputs.IsAny())
                {
                    outputs[0].CustomizeGift.TotalActive = totalGiftActive;
                    outputs[0].CustomizeGift.TotalInActive = totalGiftInActive;
                    outputs[0].CustomizeGift.TotalChangePrice = totalChangePrice;
                    outputs[0].CustomizeGift.TotalChangeEffective = totalChangeEffective;
                    outputs[0].CustomizeGift.TotalNoChange = totalNoChange;
                    outputs[0].CustomizeGift.TotalNew = totalNew;
                    outputs[0].CustomizeGift.TotalRemove = totalRemove;

                    foreach (var gift in outputs)
                    {
                        var brand = _thirdPartyBrandMapping.FirstOrDefault(x => x.IsActive == true && x.ThirdPartyBrandId == gift.CustomizeGift.ThirdPartyGiftBrandId && x.VendorId == gift.CustomizeGift.VendorId);
                        if (brand != null)
                        {
                            gift.CustomizeGift.ThirdPartyBrandName = brand.ThirdPartyBrandName;
                        }

                        if (!string.IsNullOrEmpty(gift.CustomizeGift.ThirdPartyCategoryId))
                        {
                            var integrationCategory = await integrationCat.FirstOrDefaultAsync(x => x.IntegrationId == gift.CustomizeGift.ThirdPartyCategoryId.ToString() && x.VendorId == gift.CustomizeGift.VendorId);
                            if (integrationCategory != null)
                            {
                                gift.CustomizeGift.CategoryName = integrationCategory.Title;
                            }
                        }
                        gift.CustomizeGift.InternalCategoryName = await MapInternalCategoryName(gift.CustomizeGift);
                    }
                }
            }

            return new PagedResultDto<CustomizeGiftInfosView>(
               totalCount,
               outputs
           );
        }

        [HttpPost]
        public async Task<PointFileDto> Get3rdGiftsToExcel(ThirdPartGiftGetAllForExcelInput input)
        {
            var result = new List<GetGiftCategoryForEditOutput>();
            var giftInfors = new List<CustomizeGiftInfosDto>();
            var outputs = new List<CustomizeGiftInfosView>();
            if (input.ParentCategoryCode == "00001")
            {
                var queryGifts = _giftInforRepository.GetAll()
                    .WhereIf(input.VendorId > 0, x => x.VendorId == input.VendorId)
                    .WhereIf(input.VendorId == -1, x => x.Vendor == "LinkId" || x.Vendor == "" || x.Vendor == null)
                    .WhereIf(input.BrandId > 0, x => x.BrandId == input.BrandId)
                    .WhereIf(input.ThirdPartyBrandId != null && input.ThirdPartyBrandId.Count() > 0, x => input.ThirdPartyBrandId.Contains(x.ThirdPartyBrandId))
                    .WhereIf(input.ThirdPartyCategoryId != null && input.ThirdPartyCategoryId.Count() > 0, x => input.ThirdPartyCategoryId.Contains(x.ThirdPartyCategoryId))
                    .WhereIf(!string.IsNullOrEmpty(input.ChangeStatus) && input.ChangeStatus == ChangeStatusConsts.New, x => x.Priority == 5)
                    .WhereIf(!string.IsNullOrEmpty(input.ChangeStatus) && input.ChangeStatus == ChangeStatusConsts.Removed, x => x.Priority == 4)
                    .WhereIf(!string.IsNullOrEmpty(input.ChangeStatus) && input.ChangeStatus == "Price Changed", x => x.Priority == 1)
                    .WhereIf(!string.IsNullOrEmpty(input.ChangeStatus) && input.ChangeStatus == "Effective", x => x.Priority == 2)
                    .WhereIf(!string.IsNullOrEmpty(input.ChangeStatus) && input.ChangeStatus == ChangeStatusConsts.Approved, x => x.Priority == 99)
                    .WhereIf(!string.IsNullOrEmpty(input.Status), x => x.Status == input.Status)
                    .WhereIf(!string.IsNullOrEmpty(input.GiftName), x => x.Name != null && StringUtilHelper.RemoveAccentVietnamese(x.Name.ToLower()).Contains(StringUtilHelper.RemoveAccentVietnamese(input.GiftName.ToLower())))
                    .WhereIf(!string.IsNullOrEmpty(input.GiftCode), x => x.Code.ToLower().Contains(input.GiftCode.ToLower()))
                    .WhereIf(!string.IsNullOrEmpty(input.ThirdPartyGiftCode), x => x.ThirdPartyGiftCode == input.ThirdPartyGiftCode);

                // var brands = _thirdPartyBrandMapping.GetAll().Where(x => x.IsActive == true);
                var integrationCat = _integrationCategoryMapping.GetAll().Where(x => x.Status == "A");
                var internalCat = _giftCategoryRepository.GetAll().Where(x => x.Status == "A" && !x.Is3rdPartyGiftCategory);

                var giftsTmp = from g in queryGifts
                               join c in integrationCat on new { IntegrationCategoryId = g.ThirdPartyCategoryId, VendorIdMap = g.VendorId }
                                                    equals new { IntegrationCategoryId = c.IntegrationId, VendorIdMap = c.VendorId } into gc
                               from c in gc.DefaultIfEmpty()
                               join ic in internalCat on g.FullGiftCategoryCode equals ic.Code into gic
                               from ic in gic.DefaultIfEmpty()
                               select new CustomizeGiftInfosView()
                               {
                                   CustomizeGift = CustomizMappingGifts(g, c, ic, null)
                               };

                var totalGiftActive = await giftsTmp.Where(x => x.CustomizeGift.Status == StatusConst.Active).CountAsync();
                var totalGiftInActive = await giftsTmp.Where(x => x.CustomizeGift.Status == StatusConst.InActive).CountAsync();
                var totalChangePrice = await giftsTmp.Where(x => x.CustomizeGift.Priority == 1).CountAsync();
                var totalChangeEffective = await giftsTmp.Where(x => x.CustomizeGift.Priority == 2).CountAsync();
                var totalNoChange = await giftsTmp.Where(x => x.CustomizeGift.Priority == 99 || (x.CustomizeGift.Priority == null)).CountAsync();
                var totalNew = await giftsTmp.Where(x => x.CustomizeGift.Priority == 5).CountAsync();
                var totalRemove = await giftsTmp.Where(x => x.CustomizeGift.Priority == 4).CountAsync();


                // giftInfors = giftsTmp.Skip(input.SkipCount).Take(input.MaxResultCount).ToList();
                outputs = await giftsTmp
               .OrderBy("CustomizeGift.Priority asc")
               .ToListAsync();

                var vendors = _thirdPartyGiftVendorRepository.GetAll().Where(x => x.Status == "A")
                                    .Select(v => new
                                    {
                                        VendorId = v.Id,
                                        Name = v.VendorName,
                                        CoinPriceExchangeRate = (decimal)v.CoinPriceExchangeRate
                                    }).ToList();
                if (outputs.IsAny())
                {
                    outputs[0].CustomizeGift.TotalActive = totalGiftActive;
                    outputs[0].CustomizeGift.TotalInActive = totalGiftInActive;
                    outputs[0].CustomizeGift.TotalChangePrice = totalChangePrice;
                    outputs[0].CustomizeGift.TotalChangeEffective = totalChangeEffective;
                    outputs[0].CustomizeGift.TotalNoChange = totalNoChange;
                    outputs[0].CustomizeGift.TotalNew = totalNew;
                    outputs[0].CustomizeGift.TotalRemove = totalRemove;
                }

                foreach (var gift in outputs)
                {
                    var vendor = vendors.FirstOrDefault(x => x.VendorId == gift.CustomizeGift.VendorId);
                    if (vendor != null)
                    {
                        gift.CustomizeGift.VendorName = vendor.Name ?? "";

                        if (vendor.CoinPriceExchangeRate != 0)
                        {
                            gift.CustomizeGift.SellInPrice = gift.CustomizeGift.VendorPrice / vendor.CoinPriceExchangeRate;
                            gift.CustomizeGift.FieldToCal = gift.CustomizeGift.SellInPrice;
                        }
                        else
                        {
                            gift.CustomizeGift.SellInPrice = 0;
                            gift.CustomizeGift.FieldToCal = 0;
                        }
                    }

                    var brand = _thirdPartyBrandMapping.FirstOrDefault(x => x.IsActive == true && x.ThirdPartyBrandId == gift.CustomizeGift.ThirdPartyGiftBrandId && x.VendorId == gift.CustomizeGift.VendorId);
                    if (brand != null)
                    {
                        gift.CustomizeGift.ThirdPartyBrandName = brand.ThirdPartyBrandName;
                    }

                    if (gift.CustomizeGift.VendorPriceNew.HasValue)
                    {
                        gift.CustomizeGift.SellInPriceNew = gift.CustomizeGift.VendorPriceNew / vendor.CoinPriceExchangeRate;
                    }
                }

                var roundType = GetRoundingType();


            }
            else
            {
                var queryGifts = _giftInforRepository.GetAll()
                    //.Where(x => x.Selected && x.VendorId.HasValue)
                    .WhereIf(input.BrandId > 0, x => x.BrandId == input.BrandId)
                    .WhereIf(input.VendorId > 0, x => x.VendorId == input.VendorId)
                    .WhereIf(input.VendorId == -1, x => x.Vendor == "LinkId" || x.Vendor == "" || x.Vendor == null)
                    .WhereIf(input.ThirdPartyBrandId != null && input.ThirdPartyBrandId.Count() > 0, x => input.ThirdPartyBrandId.Contains(x.ThirdPartyBrandId))
                    .WhereIf(input.ThirdPartyCategoryId != null && input.ThirdPartyCategoryId.Count() > 0, x => input.ThirdPartyCategoryId.Contains(x.ThirdPartyCategoryId))
                    .WhereIf(!string.IsNullOrEmpty(input.ParentCategoryCode), x => x.FullGiftCategoryCode == input.ParentCategoryCode)
                    .WhereIf(!string.IsNullOrEmpty(input.ChangeStatus) && input.ChangeStatus == ChangeStatusConsts.New, x => x.Priority == 5)
                    .WhereIf(!string.IsNullOrEmpty(input.ChangeStatus) && input.ChangeStatus == ChangeStatusConsts.Removed, x => x.Priority == 4)
                    .WhereIf(!string.IsNullOrEmpty(input.ChangeStatus) && input.ChangeStatus == "Price Changed", x => x.Priority == 1)
                    .WhereIf(!string.IsNullOrEmpty(input.ChangeStatus) && input.ChangeStatus == "Effective", x => x.Priority == 2)
                    .WhereIf(!string.IsNullOrEmpty(input.ChangeStatus) && input.ChangeStatus == ChangeStatusConsts.Approved, x => x.Priority == 99)
                    .WhereIf(!string.IsNullOrEmpty(input.Status), x => x.Status == input.Status)
                    .WhereIf(!string.IsNullOrEmpty(input.GiftName), x => x.Name != null && StringUtilHelper.RemoveAccentVietnamese(x.Name.ToLower()).Contains(StringUtilHelper.RemoveAccentVietnamese(input.GiftName.ToLower())))
                    .WhereIf(!string.IsNullOrEmpty(input.GiftCode), x => x.Code.ToLower().Contains(input.GiftCode.ToLower()));

                var integrationCat = _integrationCategoryMapping.GetAll().Where(x => x.Status == "A");
                var internalCat = _giftCategoryRepository.GetAll().Where(x => x.Status == "A" && !x.Is3rdPartyGiftCategory);
                //  .WhereIf(!string.IsNullOrEmpty(input.ParentCategoryCode), x => x.Code == input.ParentCategoryCode);
                var vendors = _thirdPartyGiftVendorRepository.GetAll().Where(x => x.Status == "A");
                // .WhereIf(input.VendorId > 0, x => x.Id == input.VendorId);

                var giftsTmp = from g in queryGifts
                               join c in integrationCat on new { IntegrationCategoryId = g.ThirdPartyCategoryId, VendorIdMap = g.VendorId }
                                                    equals new { IntegrationCategoryId = c.IntegrationId, VendorIdMap = c.VendorId } into gc
                               from c in gc.DefaultIfEmpty()
                               join ic in internalCat on g.FullGiftCategoryCode equals ic.Code into gic
                               from ic in gic.DefaultIfEmpty()
                               join v in vendors on g.VendorId equals v.Id into gv
                               from v in gv.DefaultIfEmpty()
                                   /*join v in vendors on g.VendorId equals v.Id*/
                               select new CustomizeGiftInfosView
                               {
                                   CustomizeGift = CustomizMappingGifts(g, c, ic, v)
                               };

                //Gifts was stored in database
                var oldGifts = giftsTmp.WhereIf(input.VendorId > 0, x => x.CustomizeGift.VendorId > 0).ToList();

                giftsTmp = giftsTmp.WhereIf(!string.IsNullOrEmpty(input.ParentCategoryCode) && input.ParentCategoryCode != "00001", x => CheckCategoryName(x.CustomizeGift))
                                .WhereIf(input.VendorId > 0, x => x.CustomizeGift.VendorId > 0);

                var totalGiftActive = await giftsTmp.Where(x => x.CustomizeGift.Status == StatusConst.Active).CountAsync();
                var totalGiftInActive = await giftsTmp.Where(x => x.CustomizeGift.Status == StatusConst.InActive).CountAsync();
                var totalChangePrice = await giftsTmp.Where(x => x.CustomizeGift.Priority == 1).CountAsync();
                var totalChangeEffective = await giftsTmp.Where(x => x.CustomizeGift.Priority == 2).CountAsync();
                var totalNoChange = await giftsTmp.Where(x => x.CustomizeGift.Priority == 99 || x.CustomizeGift.Priority == null).CountAsync();
                var totalNew = await giftsTmp.Where(x => x.CustomizeGift.Priority == 5).CountAsync();
                var totalRemove = await giftsTmp.Where(x => x.CustomizeGift.Priority == 4).CountAsync();

                outputs = await giftsTmp
               .ToListAsync();

                if (outputs.IsAny())
                {
                    outputs[0].CustomizeGift.TotalActive = totalGiftActive;
                    outputs[0].CustomizeGift.TotalInActive = totalGiftInActive;
                    outputs[0].CustomizeGift.TotalChangePrice = totalChangePrice;
                    outputs[0].CustomizeGift.TotalChangeEffective = totalChangeEffective;
                    outputs[0].CustomizeGift.TotalNoChange = totalNoChange;
                    outputs[0].CustomizeGift.TotalNew = totalNew;
                    outputs[0].CustomizeGift.TotalRemove = totalRemove;

                    foreach (var gift in outputs)
                    {
                        var brand = _thirdPartyBrandMapping.FirstOrDefault(x => x.IsActive == true && x.ThirdPartyBrandId == gift.CustomizeGift.ThirdPartyGiftBrandId && x.VendorId == gift.CustomizeGift.VendorId);
                        if (brand != null)
                        {
                            gift.CustomizeGift.ThirdPartyBrandName = brand.ThirdPartyBrandName;
                        }
                    }
                }
            }

            return _giftExcelExporter.Export3rdGiftsToExcel(outputs);
        }

        public async Task<GetDataGetGiftThirdPartyProcessingOutput> GetDataGetGiftThirdPartyProcessing()
        {
            var cacheTask = await _cache.GetStringAsync("GenerateGetThirdPartyGiftTask" + AbpSession.TenantId);
            if (!string.IsNullOrEmpty(cacheTask))
            {
                return new GetDataGetGiftThirdPartyProcessingOutput()
                {
                    IsProcessing = true,
                };
            }

            return new GetDataGetGiftThirdPartyProcessingOutput()
            {
                IsProcessing = false,
            };
        }

        public async Task FetchGiftsByCatagoryOrBrandNew(ThirdPartGiftGetAllInput input)
        {
            var cacheTask = await _cache.GetStringAsync("GenerateGetThirdPartyGiftTask" + AbpSession.TenantId);
            if (!string.IsNullOrEmpty(cacheTask))
            {
                throw new UserFriendlyException(langMutil("FetchGiftsThirdPartyIsProcessing"));
            }
            await _cache.SetStringAsync("GenerateGetThirdPartyGiftTask" + AbpSession.TenantId, JsonConvert.SerializeObject(input));
        }

        public async Task FetchGiftsByCatagoryOrBrand(ThirdPartGiftGetAllInput input)
        {
            try
            {
                if (input.VendorId > 0 && ((input.ThirdPartyCategoryId != null && input.ThirdPartyCategoryId.Count > 0) || (input.ThirdPartyBrandId != null && input.ThirdPartyBrandId.Count > 0) || !string.IsNullOrEmpty(input.ThirdPartyGiftCode)))
                {
                    var oldGifts = _giftInforRepository.GetAll().Where(x => x.Is3rdPartyGift && x.Selected && x.VendorId == input.VendorId)
                        .WhereIf(input.ThirdPartyCategoryId != null && input.ThirdPartyCategoryId.Count > 0, x => input.ThirdPartyCategoryId.Contains(x.ThirdPartyCategoryId))
                        .WhereIf(input.ThirdPartyBrandId != null && input.ThirdPartyBrandId.Count > 0, x => input.ThirdPartyBrandId.Contains(x.ThirdPartyBrandId))
                        .WhereIf(!string.IsNullOrEmpty(input.ThirdPartyGiftCode), x => x.ThirdPartyGiftCode == input.ThirdPartyGiftCode);

                    var vendor = _thirdPartyGiftVendorRepository.FirstOrDefault(x => x.Id == input.VendorId);
                    if (vendor != null)
                    {
                        var cacheCustomCharExcluder = await _cache.GetStringAsync($"LoyaltyCustomCharExcluder{AbpSession.TenantId}");
                        List<string> glstCustomCharExcluder = new List<string>();
                        if (string.IsNullOrEmpty(cacheCustomCharExcluder))
                        {
                            var customCharExcluder = await _settingsRepository.GetAll()
                                                        .Where(x => x.TenantId == AbpSession.TenantId)
                                                        .Where(x => x.Name == "Loyalty.CustomCharExcluder")
                                                        .FirstOrDefaultAsync();
                            if (customCharExcluder != null)
                            {
                                await _cache.SetStringAsync($"LoyaltyCustomCharExcluder{AbpSession.TenantId}", customCharExcluder.Value, new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromDays(30)));
                                glstCustomCharExcluder = customCharExcluder.Value.Split(";").ToList();
                            }
                        }
                        else
                        {
                            glstCustomCharExcluder = cacheCustomCharExcluder.Split(";").ToList();
                        }
                        var adapter = ThirdPartyVendorAdapterFactory.GetAdapter(vendor.Type, ObjectMapper.Map<ThirdPartyVendorApiInfo>(vendor), Logger, _unitOfWorkManager, _clientInfoProvider, _auditLogRepository, _httpContextAccessor);
                        var newGifts = new List<ThirdPartyGiftInfo>();

                        if (input.ThirdPartyCategoryId.Count > 0)
                        {
                            foreach (var thirdPartyCategory in input.ThirdPartyCategoryId)
                            {
                                var response = await adapter.GetGiftCategory(new ThirdPartyGiftCategoryRequest
                                {
                                    CategoryId = thirdPartyCategory ?? ""
                                }, vendor.TenantId);

                                if (!response.Success)
                                {
                                    Logger.Error(response.Message);
                                }
                                else
                                {
                                    if (response.Gifts != null && response.Gifts.Count > 0)
                                    {
                                        newGifts.AddRange(response.Gifts);
                                    }
                                }
                            }
                        }

                        if (input.ThirdPartyBrandId.Count > 0)
                        {
                            foreach (var thirdPartyBrandId in input.ThirdPartyBrandId)
                            {
                                var response = await adapter.GetGiftByListBrand(new ThirdPartyGiftByBrandRequest
                                {
                                    BrandId = thirdPartyBrandId ?? ""
                                }, vendor.TenantId);

                                if (!response.Success)
                                {
                                    Logger.Error(response.Message);
                                }
                                else
                                {
                                    if (response.Gifts != null && response.Gifts.Count > 0)
                                    {
                                        newGifts.AddRange(response.Gifts);
                                    }
                                }
                            }
                        }

                        if (!string.IsNullOrEmpty(input.ThirdPartyGiftCode))
                        {
                            var giftInfoResponse = await adapter.GetGiftDetail(new ThirdPartyGiftDetailRequest
                            {
                                giftId = input.ThirdPartyGiftCode
                            }, vendor.TenantId);
                            if (giftInfoResponse.Success)
                            {
                                if (vendor.Type == ThirdPartyVendorType.GotIt)
                                {
                                    newGifts.AddRange(giftInfoResponse.Gifts);
                                }
                                else
                                {
                                    newGifts.Add(giftInfoResponse.Gift);
                                }
                            }
                        }
                        List<GiftInfor> giftsRemove = new List<GiftInfor>();
                        List<ThirdPartyGiftInfo> giftUpdate = new List<ThirdPartyGiftInfo>();
                        List<ThirdPartyGiftInfo> giftInsert = new List<ThirdPartyGiftInfo>();

                        if (vendor.Type == ThirdPartyVendorType.GotIt)
                        {
                            // Get old selected Gift code & new selected Gift code
                            var oldSelectedCodes = oldGifts.Where(t => t.Selected).Select(t => new { ThirdPartyGiftCode = t.ThirdPartyGiftCode, ThirdPartyGiftType2 = t.ThirdPartyGiftType2 }).ToList();
                            var newSelectedCodes = newGifts.Where(t => t.Status == "A").Select(t => new { ThirdPartyGiftCode = t.Code, ThirdPartyGiftType2 = t.ThirdPartyGiftType2 }).ToList();

                            // Find remove gifts, create new gifts and update info gifts 
                            var needRemoveGiftCodes = oldSelectedCodes.Except(newSelectedCodes).ToList();
                            var needCreateGiftCodes = newSelectedCodes.Except(oldSelectedCodes).ToList();
                            var needUpdateGiftCodes = oldSelectedCodes.Intersect(newSelectedCodes).ToList();

                            giftsRemove = oldGifts.Where(x => needRemoveGiftCodes.Contains(new { ThirdPartyGiftCode = x.ThirdPartyGiftCode, ThirdPartyGiftType2 = x.ThirdPartyGiftType2 })).GroupBy(x => new { ThirdPartyGiftCode = x.ThirdPartyGiftCode, ThirdPartyGiftType2 = x.ThirdPartyGiftType2 }).Select(x => x.First()).ToList();
                            giftUpdate = newGifts.Where(x => needUpdateGiftCodes.Contains(new { ThirdPartyGiftCode = x.Code, ThirdPartyGiftType2 = x.ThirdPartyGiftType2 })).GroupBy(x => new { ThirdPartyGiftCode = x.Code, ThirdPartyGiftType2 = x.ThirdPartyGiftType2 }).Select(x => x.First()).ToList();
                            giftInsert = newGifts.Where(x => needCreateGiftCodes.Contains(new { ThirdPartyGiftCode = x.Code, ThirdPartyGiftType2 = x.ThirdPartyGiftType2 })).GroupBy(x => new { ThirdPartyGiftCode = x.Code, ThirdPartyGiftType2 = x.ThirdPartyGiftType2 }).Select(x => x.First()).ToList();

                        }
                        else
                        {
                            // Get old selected Gift code & new selected Gift code
                            var oldSelectedCodes = oldGifts.Where(t => t.Selected).Select(t => t.ThirdPartyGiftCode).ToList();
                            var newSelectedCodes = newGifts.Where(t => t.Status == "A").Select(t => t.Code).ToList();

                            // Find remove gifts, create new gifts and update info gifts 
                            var needRemoveGiftCodes = oldSelectedCodes.Except(newSelectedCodes).ToList();
                            var needCreateGiftCodes = newSelectedCodes.Except(oldSelectedCodes).ToList();
                            var needUpdateGiftCodes = oldSelectedCodes.Intersect(newSelectedCodes).ToList();

                            giftsRemove = oldGifts.Where(x => needRemoveGiftCodes.Contains(x.ThirdPartyGiftCode)).GroupBy(x => x.ThirdPartyGiftCode).Select(x => x.First()).ToList();
                            giftUpdate = newGifts.Where(x => needUpdateGiftCodes.Contains(x.Code)).GroupBy(x => x.Code).Select(x => x.First()).ToList();
                            giftInsert = newGifts.Where(x => needCreateGiftCodes.Contains(x.Code)).GroupBy(x => x.Code).Select(x => x.First()).ToList();

                        }
                        if (giftsRemove.Any())
                        {
                            foreach (var gift in giftsRemove)
                            {
                                var giftRemoved = await _giftInforRepository.UpdateAsync(gift.Id, g =>
                                {
                                    g.Status = "I";
                                    g.ChangeStatus = ChangeStatusConsts.Removed;
                                    g.Note3rdPartyChange = $"Gift was removed or disabled.";
                                    g.Priority = GiftChangeStatusConsts.Removed;
                                    g.RequiredCoinTemp = null;
                                    g.SellOutPriceTemp = null;
                                    g.RequiredCoinNewTemp = null;
                                    g.SellOutPriceNewTemp = null;
                                    g.DiscountPriceTemp = null;

                                    return Task.FromResult(0);
                                });
                            }
                        }

                        if (giftUpdate.Any())
                        {
                            foreach (var gift in giftUpdate)
                            {
                                var needUpdateGift = new GiftInfor();
                                if (vendor.Type == ThirdPartyVendorType.GotIt)
                                {
                                    needUpdateGift = _giftInforRepository.FirstOrDefault(x => x.VendorId == vendor.Id && x.Selected && x.ThirdPartyGiftCode == gift.Code && x.ThirdPartyGiftType2 == gift.ThirdPartyGiftType2);
                                }
                                else
                                {
                                    needUpdateGift = _giftInforRepository.FirstOrDefault(x => x.VendorId == vendor.Id && x.Selected && x.ThirdPartyGiftCode == gift.Code);
                                }
                                if (needUpdateGift == null) { continue; }
                                // Call to 3rd get full detail gift
                                var thirdPartyGiftInfo = new ThirdPartyGiftInfo();
                                if (vendor.Type == ThirdPartyVendorType.GotIt)
                                {
                                    thirdPartyGiftInfo = gift;
                                }
                                else
                                {
                                    var giftInfoResponse = await adapter.GetGiftDetail(new ThirdPartyGiftDetailRequest
                                    {
                                        giftId = gift.Code
                                    }, vendor.TenantId);

                                    if (!giftInfoResponse.Success)
                                    {
                                        Logger.Error($"[GetThirdPartyGiftForCategoryBaseline]___Call GetGiftDetail___Unsuccess: {giftInfoResponse.Message}");
                                        continue;
                                    }

                                    thirdPartyGiftInfo = giftInfoResponse.Gift;
                                }


                                var thirdPartyBrandMapping = _thirdPartyBrandMapping.GetAll()
                                                                                    .Include(x => x.Brand).FirstOrDefault(x => x.BrandId.HasValue
                                                                                                                    && x.VendorId == vendor.Id && x.IsActive
                                                                                                                    && x.ThirdPartyBrandId == gift.ThirdPartyGiftBrandId);
                                var integrationCategoryMapping = _giftCategoryRepository.GetAll()
                                                                                    .Include(x => x.Parent).FirstOrDefault(x => x.Is3rdPartyGiftCategory
                                                                                                                    && x.Status == "A" && !x.Disable
                                                                                                                    && x.VendorId == vendor.Id
                                                                                                                    && x.ParentId.HasValue
                                                                                                                    && x.ThirdPartyGiftCategoryId == gift.ThirdPartyGiftCategoryId);

                                var noteChange = string.Empty;
                                var changeStatus = string.Empty;
                                //bool isChangeStatus = false
                                var giftUpdated = await _giftInforRepository.UpdateAsync(needUpdateGift.Id, g =>
                                {
                                    g.ThirdPartyCategoryCode = integrationCategoryMapping?.Code ?? "";
                                    g.FullGiftCategoryCode = integrationCategoryMapping?.ParentCode ?? "";
                                    if (!string.IsNullOrEmpty(integrationCategoryMapping?.ParentCode))
                                    {
                                        var parentCategory = _giftCategoryRepository.GetAll().FirstOrDefault(x => x.Code == integrationCategoryMapping.ParentCode
                                                                && x.Status == "A" && !x.Disable);
                                        if (parentCategory != null && !string.IsNullOrWhiteSpace(parentCategory?.ParentCode))
                                        {
                                            g.FullGiftCategoryCode = string.Format("{0},{1}", parentCategory?.ParentCode, integrationCategoryMapping?.ParentCode);
                                        }
                                    }
                                    if (string.IsNullOrEmpty(gift.CategoryName))
                                    {
                                        var integrationCat = _integrationCategoryMapping.FirstOrDefault(x => x.IntegrationId == thirdPartyGiftInfo.ThirdPartyGiftCategoryId && x.VendorId == vendor.Id && x.Status == StatusConst.Active);
                                        if (integrationCat != null)
                                        {
                                            g.ThirdPartyCategoryName = integrationCat.Title ?? null;
                                        }
                                    }
                                    else
                                    {
                                        g.ThirdPartyCategoryName = gift.CategoryName;
                                    }

                                    if (thirdPartyBrandMapping != null)
                                    {
                                        g.Brand = thirdPartyBrandMapping.Brand;
                                        if (thirdPartyBrandMapping.Brand != null)
                                        {
                                            g.BrandId = thirdPartyBrandMapping.Brand.Id;
                                            g.BrandName = thirdPartyBrandMapping.Brand.Name;

                                            if (!string.IsNullOrEmpty(thirdPartyBrandMapping.Brand.FullGiftCategoryCode))
                                            {
                                                g.FullGiftCategoryCode = g.Brand.FullGiftCategoryCode;
                                                var cateOfBrand = _giftCategoryRepository.FirstOrDefault(x => x.Code == thirdPartyBrandMapping.Brand.FullGiftCategoryCode && x.Status == StatusConst.Active && !x.Disable);
                                                if (cateOfBrand != null && !string.IsNullOrEmpty(cateOfBrand.ParentCode))
                                                {
                                                    g.FullGiftCategoryCode = string.Format("{0},{1}", cateOfBrand.ParentCode, cateOfBrand.Code);
                                                }
                                            }
                                        }

                                        g.ThirdPartyBrandMappingId = thirdPartyBrandMapping.Id;
                                    }

                                    if (g.ChangeStatus == ChangeStatusConsts.Removed)
                                    {
                                        changeStatus = ChangeStatusConsts.New;
                                        g.Priority = GiftChangeStatusConsts.New;
                                        g.Note3rdPartyChange = "";
                                    }
                                    var roundType = GetRoundingType();

                                    if (g.Priority == GiftChangeStatusConsts.New)
                                    {
                                        var sellOutPrice = RoundExtensions.RoundByType(gift.SellInPrice * (1 + (decimal)vendor.DefaultMargin / 100), roundType);
                                        g.Margin = (decimal)vendor.DefaultMargin;
                                        g.RequiredCoin = sellOutPrice;
                                        g.FullPrice = sellOutPrice;
                                        g.DiscountPrice = 0;
                                        g.VendorPrice = gift.VendorPrice;

                                        g.SellOutPriceTemp = sellOutPrice;
                                        g.RequiredCoinTemp = sellOutPrice;
                                        g.DiscountPriceTemp = 0;
                                        g.VendorPriceNew = null;
                                        g.SellOutPriceNew = null;

                                        g.EffectiveFromNew = null;
                                        g.EffectiveToNew = null;
                                    }
                                    else if (needUpdateGift.VendorPrice != gift.VendorPrice && needUpdateGift.VendorPriceNew != gift.VendorPrice)
                                    {
                                        g.VendorPriceNew = gift.VendorPrice;
                                        // g.Margin = needUpdateGift.Margin;
                                        g.DiscountPrice = 0;
                                        g.SellOutPriceOld = needUpdateGift.SellOutPriceNew.HasValue ? needUpdateGift.SellOutPriceNew : needUpdateGift.FullPrice;
                                        g.SellOutPriceNew = RoundExtensions.RoundByType(gift.SellInPrice * (1 + needUpdateGift.Margin / 100), roundType);
                                        g.RequiredCoinOld = needUpdateGift.RequiredCoin;
                                        g.Priority = GiftChangeStatusConsts.ChangePrice;
                                        // g.ChangeStatus = ChangeStatusConsts.ChangePrice;
                                        noteChange += $" | Vendor Price was changed";
                                        changeStatus += $" | {ChangeStatusConsts.ChangePrice}";
                                        //isChangeStatus = true;

                                        g.SellOutPriceNewTemp = RoundExtensions.RoundByType(gift.SellInPrice * (1 + needUpdateGift.Margin / 100), roundType);
                                        g.DiscountPriceTemp = gift.DiscountPrice;
                                        g.RequiredCoinNewTemp = RoundExtensions.RoundByType((100 - g.DiscountPriceTemp.Value) * g.SellOutPriceNewTemp.Value / 100, roundType);
                                    }

                                    switch (vendor.Type)
                                    {
                                        case ThirdPartyVendorType.Urbox:
                                            g.EffectiveFrom = gift?.EffectiveFrom ?? DateTime.UtcNow;
                                            g.EffectiveTo = gift?.EffectiveTo ?? DateTime.UtcNow.AddYears(10);
                                            g.ThirdPartyGiftType2 = gift?.ThirdPartyGiftType2 ?? "";

                                            g.Is3rdPartyPromotion = thirdPartyGiftInfo.Is3rdPartyPromotion;

                                            if (g.Is3rdPartyPromotion)
                                            {
                                                decimal fullPrice = RoundExtensions.RoundByType(thirdPartyGiftInfo.SellInPrice * (1 + g.Margin / 100), roundType);
                                                decimal requiredCoin = RoundExtensions.RoundByType((thirdPartyGiftInfo.VendorPricePromotion.Value / (decimal)vendor.CoinPriceExchangeRate) * (1 + g.Margin / 100), roundType);
                                                decimal discountPrice = 0;

                                                if (fullPrice != 0)
                                                {
                                                    discountPrice = RoundExtensions.RoundByType(100 - ((requiredCoin * 100) / fullPrice));
                                                }

                                                g.FullPrice = fullPrice;
                                                g.RequiredCoin = requiredCoin;
                                                g.DiscountPrice = discountPrice;
                                                g.VendorPrice = gift.VendorPrice;

                                                g.SellOutPriceTemp = fullPrice;
                                                g.RequiredCoinTemp = requiredCoin;
                                                g.DiscountPriceTemp = discountPrice;
                                            }
                                            break;
                                        case ThirdPartyVendorType.Dealtoday:
                                            g.UsageCheck = true;
                                            if (g.Priority == GiftChangeStatusConsts.ChangePrice)
                                            {
                                                noteChange = $" | Vendor Price was changed";
                                                changeStatus = $" | {ChangeStatusConsts.ChangePrice}";
                                            }
                                            if (needUpdateGift.EffectiveFrom != gift.EffectiveFrom)
                                            {
                                                g.EffectiveFromNew = gift.EffectiveFrom;
                                                //nếu không phải trường hợp change giá thì update trạng thái change date
                                                if (g.Priority != GiftChangeStatusConsts.ChangePrice)
                                                {
                                                    g.Priority = GiftChangeStatusConsts.ChangeDate;
                                                }
                                                // g.ChangeStatus = ChangeStatusConsts.ChangeEffectiveFrom;
                                                noteChange += $" | Vendor Effective from was changed";
                                                changeStatus += $" | {ChangeStatusConsts.ChangeEffectiveFrom}";
                                            }

                                            if (needUpdateGift.EffectiveTo != gift.EffectiveTo)
                                            {
                                                g.EffectiveToNew = gift.EffectiveTo;
                                                //nếu không phải trường hợp change giá thì update trạng thái change date
                                                if (g.Priority != GiftChangeStatusConsts.ChangePrice)
                                                {
                                                    g.Priority = GiftChangeStatusConsts.ChangeDate;
                                                }
                                                //g.ChangeStatus = ChangeStatusConsts.ChangeEffectiveTo;
                                                noteChange += $" | Vendor Effective to was changed";
                                                changeStatus += $" | {ChangeStatusConsts.ChangeEffectiveTo}";
                                            }
                                            break;
                                        case ThirdPartyVendorType.GotIt:
                                            g.EffectiveFrom = gift?.EffectiveFrom ?? DateTime.UtcNow;
                                            g.EffectiveTo = gift?.EffectiveTo ?? DateTime.UtcNow.AddYears(10);
                                            break;
                                        case ThirdPartyVendorType.GiftPop:
                                            g.EffectiveFrom = gift?.EffectiveFrom ?? DateTime.UtcNow;
                                            g.EffectiveTo = gift?.EffectiveTo ?? DateTime.UtcNow.AddYears(10);
                                            break;
                                        default:
                                            break;
                                    }

                                    //if (vendor.Type == "Urbox")
                                    //{
                                    //    g.EffectiveFrom = gift?.EffectiveFrom ?? DateTime.UtcNow;
                                    //    g.EffectiveTo = gift?.EffectiveTo ?? DateTime.UtcNow.AddYears(10);
                                    //    g.ThirdPartyGiftType2 = gift?.ThirdPartyGiftType2 ?? "";
                                    //}
                                    //else if (vendor.Type == "Dealtoday")
                                    //{
                                    //    g.UsageCheck = true;
                                    //    if (g.Priority == GiftChangeStatusConsts.ChangePrice)
                                    //    {
                                    //        noteChange = $" | Vendor Price was changed";
                                    //        changeStatus = $" | {ChangeStatusConsts.ChangePrice}";
                                    //    }
                                    //    if (needUpdateGift.EffectiveFrom != gift.EffectiveFrom)
                                    //    {
                                    //        g.EffectiveFromNew = gift.EffectiveFrom;
                                    //        //nếu không phải trường hợp change giá thì update trạng thái change date
                                    //        if (g.Priority != GiftChangeStatusConsts.ChangePrice)
                                    //        {
                                    //            g.Priority = GiftChangeStatusConsts.ChangeDate;
                                    //        }
                                    //        // g.ChangeStatus = ChangeStatusConsts.ChangeEffectiveFrom;
                                    //        noteChange += $" | Vendor Effective from was changed";
                                    //        changeStatus += $" | {ChangeStatusConsts.ChangeEffectiveFrom}";
                                    //    }

                                    //    if (needUpdateGift.EffectiveTo != gift.EffectiveTo)
                                    //    {
                                    //        g.EffectiveToNew = gift.EffectiveTo;
                                    //        //nếu không phải trường hợp change giá thì update trạng thái change date
                                    //        if (g.Priority != GiftChangeStatusConsts.ChangePrice)
                                    //        {
                                    //            g.Priority = GiftChangeStatusConsts.ChangeDate;
                                    //        }
                                    //        //g.ChangeStatus = ChangeStatusConsts.ChangeEffectiveTo;
                                    //        noteChange += $" | Vendor Effective to was changed";
                                    //        changeStatus += $" | {ChangeStatusConsts.ChangeEffectiveTo}";
                                    //    }
                                    //}
                                    //else if (vendor.Type == ThirdPartyVendorType.GotIt)
                                    //{
                                    //    g.EffectiveFrom = gift?.EffectiveFrom ?? DateTime.UtcNow;
                                    //    g.EffectiveTo = gift?.EffectiveTo ?? DateTime.UtcNow.AddYears(10);
                                    //}

                                    if (!string.IsNullOrWhiteSpace(changeStatus))
                                    {
                                        g.ChangeStatus = changeStatus.TrimStart().TrimStart('|');
                                    }

                                    if (!string.IsNullOrWhiteSpace(noteChange))
                                    {
                                        g.Note3rdPartyChange = noteChange.TrimStart().TrimStart('|');
                                    }

                                    if (needUpdateGift.ExpireDuration != gift.ExpireDuration)
                                    {
                                        g.ExpireDuration = gift.ExpireDuration;
                                    }

                                    // ADDED new update data for 3rd gift
                                    if (needUpdateGift.ThirdPartyGiftType != thirdPartyGiftInfo.Type)
                                    {
                                        g.ThirdPartyGiftType = thirdPartyGiftInfo.Type;
                                    }
                                    if (needUpdateGift.UsageCheck != thirdPartyGiftInfo.UsageCheck)
                                    {
                                        g.UsageCheck = thirdPartyGiftInfo.UsageCheck;
                                    }
                                    if (needUpdateGift.ThirdPartyBrandName != thirdPartyGiftInfo.ThirdPartyBrandName)
                                    {
                                        g.ThirdPartyBrandName = thirdPartyGiftInfo.ThirdPartyBrandName;
                                    }
                                    if (needUpdateGift.IsEGift != thirdPartyGiftInfo.IsEGift)
                                    {
                                        g.IsEGift = thirdPartyGiftInfo.IsEGift;
                                    }
                                    if (vendor.Type.Equals(ThirdPartyVendorType.GotIt))
                                    {
                                        CultureInfo cul = CultureInfo.GetCultureInfo("vi-VN");
                                        var giftName = $"{thirdPartyGiftInfo.ThirdPartyBrandName} {g.VendorPrice.ToString("#,###", cul.NumberFormat)}";
                                        g.Name = giftName;
                                    }
                                    else
                                    {    
                                        foreach (var item in glstCustomCharExcluder)
                                        {
                                            thirdPartyGiftInfo.Name = StringUtilHelper.removeSpecificCharacter(thirdPartyGiftInfo.Name, item);
                                        }
                                    }
                                    g.Name = thirdPartyGiftInfo.Name;
                                    g.Introduce = thirdPartyGiftInfo.Introduce;
                                    g.Description = thirdPartyGiftInfo.Description;
                                    g.Office = thirdPartyGiftInfo.Office?.ToJsonString() ?? "";
                                    g.Condition = thirdPartyGiftInfo.Condition;
                                    g.OfficeAddress = thirdPartyGiftInfo.OfficeAddress;
                                    var regionIds = thirdPartyGiftInfo.CityId?.Split(";").Select(x => x).Distinct().ToList();
                                    if (regionIds != null)
                                    {
                                        var regionCode = new StringBuilder();
                                        foreach (var region in regionIds)
                                        {
                                            if (ThirdPartyGiftConsts.UrboxRegion.ContainsKey(region))
                                            {
                                                regionCode.Append(ThirdPartyGiftConsts.UrboxRegion[region]);
                                                regionCode.Append(";");
                                            }
                                            else continue;
                                        }

                                        var regionStd = regionCode.ToString().TrimEnd(' ').TrimEnd(';');
                                        if (regionStd != needUpdateGift.RegionCode)
                                        {
                                            g.RegionCode = regionStd;
                                        }
                                    }
                                    // End ADDED new update data for 3rd gift
                                    // Update quantity
                                    g.TotalQuantity = thirdPartyGiftInfo.RemainingQuantity + g.UsedQuantity;
                                    g.RemainingQuantity = thirdPartyGiftInfo.RemainingQuantity;

                                    g.ThirdPartyCategoryId = thirdPartyGiftInfo.ThirdPartyGiftCategoryId;
                                    if (string.IsNullOrEmpty(g.ThirdPartyCategoryName))
                                    {
                                        var integrationCat = _integrationCategoryMapping.FirstOrDefault(x => x.IntegrationId == thirdPartyGiftInfo.ThirdPartyGiftCategoryId && x.VendorId == vendor.Id && x.Status == StatusConst.Active);
                                        if (integrationCat != null)
                                        {
                                            g.ThirdPartyCategoryName = integrationCat.Title ?? null;
                                        }
                                    }

                                    if (g.ContactHotline != vendor.HotLine)
                                    {
                                        g.ContactHotline = vendor.HotLine;
                                    }

                                    if (g.ContactEmail != vendor.MailVendor)
                                    {
                                        g.ContactEmail = vendor.MailVendor;
                                    }

                                    g.LastModificationTime = DateTime.UtcNow;

                                    return Task.FromResult(0);
                                });

                                if (thirdPartyGiftInfo.GiftUsageAddressMap != null && thirdPartyGiftInfo.GiftUsageAddressMap.Any())
                                {
                                    await _giftUsageAddressAppService.SettingGiftUsageAddress(thirdPartyGiftInfo.GiftUsageAddressMap, vendor.Id, giftUpdated.Code);
                                }

                                try
                                {
                                    if (thirdPartyGiftInfo.GiftMultiLanguageMappings != null && thirdPartyGiftInfo.GiftMultiLanguageMappings.Any())
                                    {
                                        foreach (var giftMultiLanguage in thirdPartyGiftInfo.GiftMultiLanguageMappings)
                                        {
                                            await _giftMultiLanguageAppService.DeleteByGiftId(giftUpdated.Id);
                                            CreateOrEditImageLinkDto imageLink = null;
                                            if (!string.IsNullOrEmpty(giftMultiLanguage.ImageLink))
                                            {
                                                imageLink = new CreateOrEditImageLinkDto()
                                                {
                                                    FullLink = giftMultiLanguage.ImageLink,
                                                    Link = giftMultiLanguage.ImageLink,
                                                    Type = nameof(GiftInfoMultiLanguage),
                                                    isActive = true,
                                                    Code = nameof(GiftInfoMultiLanguage)
                                                };
                                            }
                                            string giftName = giftMultiLanguage.GiftName;
                                            foreach (var item in glstCustomCharExcluder)
                                            {
                                                giftName = StringUtilHelper.removeSpecificCharacter(giftName, item);
                                            }
                                            var createGiftMultiLanRequest = new CreateOrEditGiftMultiLanguageRequest()
                                            {
                                                Condition = giftMultiLanguage.Condition,
                                                Introduce = giftMultiLanguage.Introduce,
                                                Description = giftMultiLanguage.Description,
                                                GiftId = giftUpdated.Id,
                                                GiftName = giftName,
                                                Language = giftMultiLanguage.Language,
                                                ImageLinks = (imageLink != null) ? new List<CreateOrEditImageLinkDto>() { imageLink } : null
                                            };
                                            await _giftMultiLanguageAppService.CreateOrEdit(createGiftMultiLanRequest);
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Logger.Error($"ERROR_GiftMultiLanguage:{ex.Message}-{ex.StackTrace}");
                                }

                                // Update image link with urbox
                                var images = _imageLinkRepository.GetAll().Where(s => s.Code == needUpdateGift.Code && s.Type == "Gift").ToList();
                                if (images != null)
                                {
                                    //lấy image cũ để ghi log chỉnh sửa
                                    foreach (var item in images)
                                    {
                                        await _imageLinkRepository.DeleteAsync(item.Id);
                                    }

                                }
                                var imageLinks = new List<CreateOrEditImageLinkDto>
                                {
                                    new CreateOrEditImageLinkDto
                                    {
                                        Type = "Gift",
                                        FullLink = gift.ImageLink,
                                        Link = gift.ImageLink,
                                        isActive = true,
                                        Code = needUpdateGift.Code,
                                    }
                                };
                                await CreateImageLink(needUpdateGift.Code, imageLinks, vendor.TenantId ?? 0);
                                // End update image link
                            }
                        }

                        if (giftInsert.Any())
                        {
                            using (CurrentUnitOfWork.SetTenantId(AbpSession.TenantId ?? 0))
                            {
                                foreach (var gift in giftInsert)
                                {
                                    var thirdPartyGiftInfo = new ThirdPartyGiftInfo();
                                    if (vendor.Type == ThirdPartyVendorType.GotIt)
                                    {
                                        thirdPartyGiftInfo = gift;
                                    }
                                    else
                                    {
                                        var giftInfoResponse = await adapter.GetGiftDetail(new ThirdPartyGiftDetailRequest
                                        {
                                            giftId = gift.Code
                                        }, vendor.TenantId);

                                        if (!giftInfoResponse.Success)
                                        {
                                            Logger.Error($"[GetThirdPartyGiftForCategoryBaseline]___Call GetGiftDetail___Unsuccess: {giftInfoResponse.Message}");
                                            continue;
                                        }

                                        thirdPartyGiftInfo = giftInfoResponse.Gift;
                                    }
                                    var giftInfo = ObjectMapper.Map<GiftInfor>(thirdPartyGiftInfo);
                                    giftInfo.Code = _codeGeneratorAppService.GenerateCode(typeof(GiftInfor));

                                    giftInfo.TenantId = vendor.TenantId;

                                    var thirdPartyBrandMapping = _thirdPartyBrandMapping.GetAll().Include(x => x.Brand)
                                            .FirstOrDefault(x => x.ThirdPartyBrandId == thirdPartyGiftInfo.ThirdPartyGiftBrandId && x.VendorId == vendor.Id);
                                    if (thirdPartyBrandMapping != null)
                                    {
                                        giftInfo.ThirdPartyBrandMappingId = thirdPartyBrandMapping.Id;
                                        if (thirdPartyBrandMapping.Brand != null)
                                        {
                                            giftInfo.BrandId = thirdPartyBrandMapping.BrandId;
                                            giftInfo.BrandName = thirdPartyBrandMapping.Brand?.Name ?? "";

                                            if (!string.IsNullOrEmpty(thirdPartyBrandMapping.Brand.FullGiftCategoryCode))
                                            {
                                                giftInfo.FullGiftCategoryCode = thirdPartyBrandMapping.Brand.FullGiftCategoryCode;
                                            }
                                        }
                                    }

                                    giftInfo.TotalQuantity = thirdPartyGiftInfo.TotalQuantity;
                                    giftInfo.RemainingQuantity = thirdPartyGiftInfo.TotalQuantity;
                                    giftInfo.UsedQuantity = giftInfo.TotalQuantity - giftInfo.RemainingQuantity;
                                    giftInfo.Margin = (decimal)vendor.DefaultMargin;
                                    giftInfo.VendorPrice = thirdPartyGiftInfo.VendorPrice;
                                    giftInfo.ThirdPartyGiftType = thirdPartyGiftInfo.Type;

                                    var roundType = GetRoundingType();
                                    var sellOutPrice = RoundExtensions.RoundByType(thirdPartyGiftInfo.SellInPrice * (1 + giftInfo.Margin / 100), roundType);
                                    giftInfo.RequiredCoin = sellOutPrice;
                                    giftInfo.FullPrice = sellOutPrice;
                                    giftInfo.DiscountPrice = 0;

                                    giftInfo.RequiredCoinTemp = sellOutPrice;
                                    giftInfo.SellOutPriceTemp = sellOutPrice;
                                    giftInfo.DiscountPriceTemp = 0;

                                    // giftInfo.Status = "A" ;
                                    //giftInfo.Status = newGift.Status;
                                    giftInfo.Is3rdPartyGift = true;
                                    giftInfo.Selected = true;
                                    //giftInfo.FullGiftCategoryCode = giftCategory.Parent?.Code;
                                    giftInfo.VendorId = vendor.Id;
                                    giftInfo.Vendor = vendor.VendorName;
                                    giftInfo.ThirdPartyGiftCode = thirdPartyGiftInfo.Code;

                                    if (vendor.Type.Equals(ThirdPartyVendorType.GotIt))
                                    {
                                        CultureInfo cul = CultureInfo.GetCultureInfo("vi-VN");
                                        var giftName = $"{thirdPartyGiftInfo.ThirdPartyBrandName} {giftInfo.VendorPrice.ToString("#,###", cul.NumberFormat)}";
                                        giftInfo.Name = giftName;
                                    }
                                    else
                                    {
                                        foreach (var item in glstCustomCharExcluder)
                                        {
                                            giftInfo.Name = StringUtilHelper.removeSpecificCharacter(giftInfo.Name, item);
                                        }
                                    }

                                    var giftCategory = _giftCategoryRepository.FirstOrDefault(x => x.VendorId == vendor.Id && x.ThirdPartyGiftCategoryId == gift.ThirdPartyGiftCategoryId && x.Status == StatusConst.Active);
                                    if (giftCategory != null)
                                    {
                                        giftInfo.ThirdPartyCategoryCode = giftCategory.Code;
                                        giftInfo.FullGiftCategoryCode = giftCategory?.ParentCode ?? "";
                                        // giftInfo.CategoryName = giftCategory?.Parent?.Name ?? "";
                                        if (!string.IsNullOrEmpty(giftCategory?.ParentCode))
                                        {
                                            var parentCategory = _giftCategoryRepository.GetAll().FirstOrDefault(x => x.Code == giftCategory.ParentCode
                                                                    && x.Status == "A" && !x.Disable);
                                            if (parentCategory != null && !string.IsNullOrWhiteSpace(parentCategory?.ParentCode))
                                            {
                                                giftInfo.FullGiftCategoryCode = string.Format("{0},{1}", parentCategory?.ParentCode, giftCategory?.ParentCode);
                                            }
                                        }
                                    }

                                    if (thirdPartyBrandMapping != null)
                                    {
                                        giftInfo.ThirdPartyBrandMappingId = thirdPartyBrandMapping.Id;
                                        if (thirdPartyBrandMapping.Brand != null)
                                        {
                                            giftInfo.BrandId = thirdPartyBrandMapping.BrandId;
                                            giftInfo.BrandName = thirdPartyBrandMapping.Brand?.Name ?? "";

                                            if (!string.IsNullOrEmpty(thirdPartyBrandMapping.Brand.FullGiftCategoryCode))
                                            {
                                                giftInfo.FullGiftCategoryCode = thirdPartyBrandMapping.Brand.FullGiftCategoryCode;
                                                var cateOfBrand = _giftCategoryRepository.FirstOrDefault(x => x.Code == thirdPartyBrandMapping.Brand.FullGiftCategoryCode && x.Status == StatusConst.Active && !x.Disable);
                                                if (cateOfBrand != null && !string.IsNullOrEmpty(cateOfBrand.ParentCode))
                                                {
                                                    giftInfo.FullGiftCategoryCode = string.Format("{0},{1}", cateOfBrand.ParentCode, cateOfBrand.Code);
                                                }
                                            }
                                        }
                                    }

                                    giftInfo.ThirdPartyCategoryId = thirdPartyGiftInfo.ThirdPartyGiftCategoryId;
                                    var integrationCat = _integrationCategoryMapping.FirstOrDefault(x => x.IntegrationId == thirdPartyGiftInfo.ThirdPartyGiftCategoryId && x.VendorId == vendor.Id && x.Status == StatusConst.Active);
                                    if (integrationCat != null)
                                    {
                                        // giftInfo.ThirdPartyCategoryId = integrationCat.IntegrationId;
                                        giftInfo.ThirdPartyCategoryName = integrationCat.Title ?? null;
                                    }

                                    /*                       var integrationCat = _integrationCategoryMapping.FirstOrDefault(x => x.IntegrationId == gift.ThirdPartyGiftCategoryId && x.VendorId == vendor.Id);
                                                            if (integrationCat != null)
                                                            {
                                                                // giftInfo.ThirdPartyCategoryId = integrationCat.IntegrationId;
                                                                giftInfo.ThirdPartyCategoryName = integrationCat.CategoryTitle;
                                                            }*/

                                    switch (vendor.Type)
                                    {
                                        case ThirdPartyVendorType.Urbox:
                                            giftInfo.EffectiveFrom = thirdPartyGiftInfo.EffectiveFrom ?? DateTime.Now;
                                            giftInfo.EffectiveTo = thirdPartyGiftInfo.EffectiveTo ?? DateTime.Now.AddYears(10);
                                            giftInfo.ThirdPartyGiftType2 = thirdPartyGiftInfo.ThirdPartyGiftType2;

                                            giftInfo.Is3rdPartyPromotion = thirdPartyGiftInfo.Is3rdPartyPromotion;
                                            if (giftInfo.Is3rdPartyPromotion)
                                            {
                                                decimal fullPrice = RoundExtensions.RoundByType(thirdPartyGiftInfo.SellInPrice * (1 + giftInfo.Margin / 100), roundType);
                                                decimal requiredCoin = RoundExtensions.RoundByType((thirdPartyGiftInfo.VendorPricePromotion.Value / (decimal)vendor.CoinPriceExchangeRate) * (1 + giftInfo.Margin / 100), roundType);
                                                decimal discountPrice = 0;

                                                if (fullPrice != 0)
                                                {
                                                    discountPrice = RoundExtensions.RoundByType(100 - ((requiredCoin * 100) / fullPrice), roundType);
                                                }

                                                giftInfo.FullPrice = fullPrice;
                                                giftInfo.RequiredCoin = requiredCoin;
                                                giftInfo.DiscountPrice = discountPrice;
                                                giftInfo.VendorPrice = requiredCoin;

                                                giftInfo.SellOutPriceTemp = fullPrice;
                                                giftInfo.RequiredCoinTemp = requiredCoin;
                                                giftInfo.DiscountPriceTemp = discountPrice;
                                            }
                                            break;
                                        case ThirdPartyVendorType.Dealtoday:
                                            giftInfo.EffectiveFrom = thirdPartyGiftInfo.EffectiveFrom.Value;
                                            giftInfo.EffectiveTo = thirdPartyGiftInfo.EffectiveTo.Value;
                                            break;
                                        case ThirdPartyVendorType.GotIt:
                                            giftInfo.EffectiveFrom = gift?.EffectiveFrom ?? DateTime.UtcNow;
                                            giftInfo.EffectiveTo = gift?.EffectiveTo ?? DateTime.UtcNow.AddYears(10);
                                            break;
                                        case ThirdPartyVendorType.GiftPop:
                                            giftInfo.EffectiveFrom = gift?.EffectiveFrom ?? DateTime.UtcNow;
                                            giftInfo.EffectiveTo = gift?.EffectiveTo ?? DateTime.UtcNow.AddYears(10);
                                            break;
                                        default:
                                            break;
                                    }

                                    // Set constant value
                                    //if (vendor.Type == "Urbox")
                                    //{
                                    //    giftInfo.EffectiveFrom = thirdPartyGiftInfo.EffectiveFrom ?? DateTime.Now;
                                    //    giftInfo.EffectiveTo = thirdPartyGiftInfo.EffectiveTo ?? DateTime.Now.AddYears(10);
                                    //    giftInfo.ThirdPartyGiftType2 = thirdPartyGiftInfo.ThirdPartyGiftType2;
                                    //}
                                    //else if (vendor.Type == "Dealtoday")
                                    //{
                                    //    giftInfo.EffectiveFrom = thirdPartyGiftInfo.EffectiveFrom.Value;
                                    //    giftInfo.EffectiveTo = thirdPartyGiftInfo.EffectiveTo.Value;
                                    //}
                                    //else if (vendor.Type == ThirdPartyVendorType.GotIt)
                                    //{
                                    //    giftInfo.EffectiveFrom = gift?.EffectiveFrom ?? DateTime.UtcNow;
                                    //    giftInfo.EffectiveTo = gift?.EffectiveTo ?? DateTime.UtcNow.AddYears(10);
                                    //}

                                    //giftInfo.FullPrice = newGift.FullPrice;
                                    //giftInfo.DiscountPrice = newGift.DiscountPrice;
                                    //giftInfo.Tag = newGift.Tag;                   
                                    giftInfo.Status = "I";
                                    giftInfo.Introduce = thirdPartyGiftInfo.Introduce;
                                    giftInfo.Office = thirdPartyGiftInfo.Office?.ToJsonString() ?? "";
                                    giftInfo.Condition = thirdPartyGiftInfo.Condition;
                                    giftInfo.OfficeAddress = thirdPartyGiftInfo.OfficeAddress;
                                    var regionIds = thirdPartyGiftInfo.CityId?.Split(";").Select(x => x).Distinct().ToList();
                                    if (regionIds != null)
                                    {
                                        var regionCode = new StringBuilder();
                                        foreach (var region in regionIds)
                                        {
                                            if (ThirdPartyGiftConsts.UrboxRegion.ContainsKey(region))
                                            {
                                                regionCode.Append(ThirdPartyGiftConsts.UrboxRegion[region]);
                                                regionCode.Append(";");
                                            }
                                            else continue;
                                        }

                                        var regionStd = regionCode.ToString().TrimEnd(' ').TrimEnd(';');
                                        giftInfo.RegionCode = regionStd;
                                    }

                                    giftInfo.ExpireDuration = thirdPartyGiftInfo.ExpireDuration;
                                    giftInfo.LastModificationTime = DateTime.UtcNow;
                                    giftInfo.CreationTime = DateTime.UtcNow;
                                    giftInfo.ChangeStatus = ChangeStatusConsts.New;
                                    giftInfo.Priority = GiftChangeStatusConsts.New;
                                    giftInfo.GiftType = GiftInforConsts.GiftTypeMarket;
                                    //Thông tin BrandID bên 3rd
                                    giftInfo.ThirdPartyBrandId = thirdPartyGiftInfo.ThirdPartyGiftBrandId;
                                    var checkExsistGift = new GiftInfor();
                                    if (vendor.Type == ThirdPartyVendorType.GotIt)
                                    {
                                        checkExsistGift = _giftInforRepository.GetAll().AsNoTracking()
                                        .FirstOrDefault(x => x.VendorId == vendor.Id && x.ThirdPartyGiftCode == thirdPartyGiftInfo.Code && x.ThirdPartyGiftType2 == thirdPartyGiftInfo.ThirdPartyGiftType2);
                                    }
                                    else
                                    {
                                        checkExsistGift = _giftInforRepository.GetAll().AsNoTracking()
                                        .FirstOrDefault(x => x.VendorId == vendor.Id && x.ThirdPartyGiftCode == thirdPartyGiftInfo.Code);
                                    }

                                    if (checkExsistGift != null)
                                    {
                                        if (checkExsistGift.ChangeStatus == ChangeStatusConsts.Removed)
                                        {
                                            var updateRemoveGift = checkExsistGift;
                                            // Lúc này updateRemoveGift tương đương với bản ghi ở phía DB
                                            // giftInfo là bản ghi dùng ObjectMapper tạo từ dữ liệu adapters trả về.
                                            // Và ben trên có handle vài trường cho đối tượng giftInfo này. Ta cần set
                                            // vào bản ghi db những change tương ứng.
                                            updateRemoveGift.TotalQuantity = giftInfo.TotalQuantity;
                                            updateRemoveGift.RemainingQuantity = updateRemoveGift.TotalQuantity -
                                            updateRemoveGift.UsedQuantity;
                                            updateRemoveGift.ThirdPartyBrandMappingId = giftInfo.ThirdPartyBrandMappingId;
                                            updateRemoveGift.BrandId = giftInfo.BrandId;
                                            updateRemoveGift.BrandName = giftInfo.BrandName;
                                            updateRemoveGift.VendorPrice = giftInfo.VendorPrice;
                                            updateRemoveGift.Margin = giftInfo.Margin;
                                            updateRemoveGift.ThirdPartyGiftType = giftInfo.ThirdPartyGiftType;
                                            updateRemoveGift.RequiredCoin = giftInfo.RequiredCoin;
                                            updateRemoveGift.FullPrice = giftInfo.FullPrice;
                                            updateRemoveGift.DiscountPrice = giftInfo.DiscountPrice;
                                            updateRemoveGift.SellOutPriceTemp = giftInfo.SellOutPriceTemp;
                                            updateRemoveGift.RequiredCoinTemp = giftInfo.RequiredCoinTemp;
                                            updateRemoveGift.DiscountPriceTemp = giftInfo.DiscountPriceTemp;
                                            updateRemoveGift.Selected = giftInfo.Selected;
                                            updateRemoveGift.Name = giftInfo.Name;
                                            updateRemoveGift.ThirdPartyCategoryCode = giftInfo.ThirdPartyCategoryCode;
                                            updateRemoveGift.FullGiftCategoryCode = giftInfo.FullGiftCategoryCode;
                                            updateRemoveGift.ThirdPartyCategoryId = giftInfo.ThirdPartyCategoryId;
                                            updateRemoveGift.ThirdPartyCategoryName = giftInfo.ThirdPartyCategoryName;
                                            updateRemoveGift.EffectiveFrom = giftInfo.EffectiveFrom;
                                            updateRemoveGift.EffectiveTo = giftInfo.EffectiveTo;
                                            updateRemoveGift.ThirdPartyGiftType2 = giftInfo.ThirdPartyGiftType2;
                                            updateRemoveGift.Status = giftInfo.Status;
                                            updateRemoveGift.Introduce = giftInfo.Introduce;
                                            updateRemoveGift.Office = giftInfo.Office;
                                            updateRemoveGift.RegionCode = giftInfo.RegionCode;
                                            updateRemoveGift.ExpireDuration = giftInfo.ExpireDuration;
                                            updateRemoveGift.LastModificationTime = giftInfo.LastModificationTime;
                                            updateRemoveGift.ChangeStatus = ChangeStatusConsts.New;
                                            updateRemoveGift.Priority = GiftChangeStatusConsts.New;
                                            updateRemoveGift.GiftType = GiftInforConsts.GiftTypeMarket;
                                            updateRemoveGift.ThirdPartyBrandId = thirdPartyGiftInfo.ThirdPartyGiftBrandId;
                                            updateRemoveGift.Note3rdPartyChange = "";
                                            Logger.Info(" >> [BLOCK1] Data of the Gift to be removed: " + updateRemoveGift.Code + ": JSON = " + JsonConvert.SerializeObject(updateRemoveGift));
                                            _ = await _giftInforRepository.UpdateAsync(updateRemoveGift);
                                            await _unitOfWorkManager.Current.SaveChangesAsync();

                                            continue;
                                        }
                                        else
                                        {
                                            checkExsistGift.ThirdPartyBrandMappingId = giftInfo.ThirdPartyBrandMappingId ?? null;
                                            checkExsistGift.ThirdPartyCategoryCode = giftInfo.ThirdPartyCategoryCode ?? null;
                                            _ = await _giftInforRepository.UpdateAsync(checkExsistGift);
                                            _unitOfWorkManager.Current.SaveChanges();

                                            continue;
                                        }
                                    }

                                    if (thirdPartyGiftInfo.GiftUsageAddressMap != null && thirdPartyGiftInfo.GiftUsageAddressMap.Any())
                                    {
                                        await _giftUsageAddressAppService.SettingGiftUsageAddress(thirdPartyGiftInfo.GiftUsageAddressMap, vendor.Id, giftInfo.Code);
                                    }

                                    giftInfo.ContactHotline = vendor.HotLine;
                                    giftInfo.ContactEmail = vendor.MailVendor;

                                    var giftId = await _giftInforRepository.InsertAndGetIdAsync(giftInfo);

                                    try
                                    {
                                        if (thirdPartyGiftInfo.GiftMultiLanguageMappings != null && thirdPartyGiftInfo.GiftMultiLanguageMappings.Any())
                                        {
                                            foreach (var giftMultiLanguage in thirdPartyGiftInfo.GiftMultiLanguageMappings)
                                            {
                                                CreateOrEditImageLinkDto imageLink = null;
                                                if (!string.IsNullOrEmpty(giftMultiLanguage.ImageLink))
                                                {
                                                    imageLink = new CreateOrEditImageLinkDto()
                                                    {
                                                        FullLink = giftMultiLanguage.ImageLink,
                                                        Link = giftMultiLanguage.ImageLink
                                                    };
                                                }
                                                string giftName = giftMultiLanguage.GiftName;
                                                foreach (var item in glstCustomCharExcluder)
                                                {
                                                    giftName = StringUtilHelper.removeSpecificCharacter(giftName, item);
                                                }
                                                var createGiftMultiLanRequest = new CreateOrEditGiftMultiLanguageRequest()
                                                {
                                                    Condition = giftMultiLanguage.Condition,
                                                    Introduce = giftMultiLanguage.Introduce,
                                                    Description = giftMultiLanguage.Description,
                                                    GiftId = giftId,
                                                    GiftName = giftName,
                                                    Language = giftMultiLanguage.Language,
                                                    ImageLinks = (imageLink != null) ? new List<CreateOrEditImageLinkDto>() { imageLink } : null
                                                };
                                                await _giftMultiLanguageAppService.CreateOrEdit(createGiftMultiLanRequest);
                                            }
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        Logger.Error($"ERROR_GiftMultiLanguage:{ex.Message}-{ex.StackTrace}");
                                    }

                                    _unitOfWorkManager.Current.SaveChanges();
                                    var imageLinks = new List<CreateOrEditImageLinkDto>
                                {
                                    new CreateOrEditImageLinkDto
                                    {
                                         Type = "Gift",
                                         FullLink = thirdPartyGiftInfo.ImageLink,
                                         Link = thirdPartyGiftInfo.ImageLink,
                                         isActive = true,
                                         Code = giftInfo.Code,
                                    }

                                    };
                                    await CreateImageLink(giftInfo.Code, imageLinks, vendor.TenantId ?? 0);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"API___[FetchGiftsByCatagoryOrBrand]___Exception: {ex}");
            }
        }

        public async Task ApproveGifts(CustomizeGiftInfosUpdate input)
        {
            try
            {
                if (input.CustomizeGiftsApproved != null && input.CustomizeGiftsApproved.Any())
                {
                    using (CurrentUnitOfWork.SetTenantId(AbpSession.TenantId ?? 0))
                    {
                        foreach (var gift in input.CustomizeGiftsApproved)
                        {
                            var giftInfors = await _giftInforRepository.FirstOrDefaultAsync(x => x.Id == gift.Id.Value);
                            if (giftInfors == null) { continue; }

                            if (giftInfors.DiscountPriceTemp.HasValue)
                            {
                                giftInfors.DiscountPrice = giftInfors.DiscountPriceTemp.Value;
                            }

                            if (giftInfors.Priority == GiftChangeStatusConsts.ChangePrice) // Gift Change Price
                            {
                                if (giftInfors.SellOutPriceNewTemp.HasValue)
                                {
                                    giftInfors.SellOutPriceTemp = giftInfors.SellOutPriceNewTemp.Value;
                                    giftInfors.FullPrice = giftInfors.SellOutPriceNewTemp.Value;
                                    giftInfors.SellOutPriceNewTemp = null;
                                }
                                else
                                {
                                    giftInfors.FullPrice = (giftInfors.VendorPriceNew.HasValue) ? giftInfors.VendorPriceNew.Value : giftInfors.VendorPrice;
                                }
                                if (giftInfors.RequiredCoinNewTemp.HasValue)
                                {
                                    giftInfors.RequiredCoinTemp = giftInfors.RequiredCoinNewTemp.Value;
                                    giftInfors.RequiredCoin = giftInfors.RequiredCoinNewTemp.Value;
                                    giftInfors.RequiredCoinNewTemp = null;
                                }
                                else
                                {
                                    var roundType = GetRoundingType();
                                    giftInfors.RequiredCoin = RoundExtensions.RoundByType((100 - giftInfors.DiscountPrice) * giftInfors.FullPrice / 100, roundType);
                                }
                            }
                            if (giftInfors.Priority == GiftChangeStatusConsts.New) // Gift New
                            {
                                if (giftInfors.SellOutPriceTemp.HasValue)
                                {
                                    giftInfors.FullPrice = giftInfors.SellOutPriceTemp.Value;
                                }
                                else
                                {
                                    giftInfors.FullPrice = (giftInfors.VendorPriceNew.HasValue) ? giftInfors.VendorPriceNew.Value : giftInfors.VendorPrice;
                                }
                                if (giftInfors.RequiredCoinTemp.HasValue)
                                {
                                    giftInfors.RequiredCoin = giftInfors.RequiredCoinTemp.Value;
                                }
                                else
                                {
                                    var roundType = GetRoundingType();
                                    giftInfors.RequiredCoin = RoundExtensions.RoundByType((100 - giftInfors.DiscountPrice) * giftInfors.FullPrice / 100, roundType);
                                }
                            }


                            if (gift.SellOutPriceNew.HasValue)
                            {
                                giftInfors.SellOutPriceNew = null;

                                if (giftInfors.VendorPriceNew.HasValue)
                                {
                                    giftInfors.VendorPrice = giftInfors.VendorPriceNew.Value;
                                    giftInfors.VendorPriceNew = null;
                                }

                                if (gift.Margin != giftInfors.Margin)
                                {
                                    giftInfors.Margin = gift.Margin;
                                    giftInfors.IsDefaultMargin = false;
                                }

                                if (giftInfors.Priority != GiftChangeStatusConsts.New && giftInfors.Priority != GiftChangeStatusConsts.ChangePrice)
                                {
                                    giftInfors.FullPrice = giftInfors.VendorPrice;
                                    var roundType = GetRoundingType();
                                    giftInfors.RequiredCoin = RoundExtensions.RoundByType((100 - giftInfors.DiscountPrice) * giftInfors.FullPrice / 100, roundType);
                                }
                            }

                            if (giftInfors.EffectiveFromNew.HasValue)
                            {
                                giftInfors.EffectiveFrom = giftInfors.EffectiveFromNew.Value;
                                giftInfors.EffectiveFromNew = null;
                            }

                            if (giftInfors.EffectiveToNew.HasValue)
                            {
                                giftInfors.EffectiveTo = giftInfors.EffectiveToNew.Value;
                                giftInfors.EffectiveToNew = null;
                            }

                            giftInfors.Selected = true;
                            giftInfors.ChangeStatus = ChangeStatusConsts.Approved;
                            giftInfors.Priority = GiftChangeStatusConsts.Approved;
                            giftInfors.Note3rdPartyChange = "";
                            _ = await _giftInforRepository.UpdateAsync(giftInfors);
                            _unitOfWorkManager.Current.SaveChanges();
                        }
                    }
                }

                /*if (input.CustomizeGiftsRejected != null && input.CustomizeGiftsRejected.Count > 0)
                {
                    foreach (var gift in input.CustomizeGiftsApproved)
                    {
                        var giftInfors = await _giftInforRepository.FirstOrDefaultAsync(x => x.Id == gift.Id.Value);
                        if (giftInfors == null) { continue; }

                        giftInfors.Status = "I";
                        giftInfors.ChangeStatus = "No Change";
                        giftInfors.Priority = 99;
                    }
                }*/
            }
            catch (Exception ex)
            {
                Logger.Error($"API___[ApproveGift]___Exception: {ex.Message} _ {ex.StackTrace}");
            }
        }
        private async Task CreateImageLink(string giftCode, List<CreateOrEditImageLinkDto> images, int? TenantId)
        {
            int index = 0;
            List<ImageUploadInfo> imageInfo = new List<ImageUploadInfo>();
            foreach (var item in images)
            {
                var entity = ObjectMapper.Map<ImageLink>(item);
                entity.Code = giftCode;
                entity.FullLink = item.Link;
                entity.Ordinal = ++index;

                if (TenantId != null)
                {
                    entity.TenantId = TenantId;
                }
                await _imageLinkRepository.InsertAsync(entity);
                _unitOfWorkManager.Current.SaveChanges();
                imageInfo.Add(new ImageUploadInfo()
                {
                    FileName = item.Link,
                });
            }

            /*            EventBus.Trigger(new UploadImageEventData()
                        {
                            FileInfos = imageInfo
                        });*/
        }
        public async Task<List<GetGiftCategoryForEditOutput>> GetFetchGiftCategoryByParentCodeForEdit_New(ThirdPartyGetFetchMultipleGiftCategoriesForEditInput input)
        {
            var result = new List<GetGiftCategoryForEditOutput>();
            var queryVendorIdFilter = string.Empty;
            var queryThirdPartyCategoryIdFilter = string.Empty;
            var queryThirdPartyBrandIdFilter = string.Empty;

            if (input.VendorId > 0)
            {
                queryVendorIdFilter = string.Format(" AND `gc`.`VendorId` = '{0}'", input.VendorId);
            }

            if (input.ThirdPartyCategoryId.IsAny())
            {
                queryThirdPartyCategoryIdFilter = string.Format(" AND `gc`.`ThirdPartyGiftCategoryId` = '{0}'", input.ThirdPartyCategoryId);
            }

            if (input.ThirdPartyBrandId.IsAny())
            {
                queryThirdPartyBrandIdFilter = string.Format(" AND `gc`.`ThirdPartyGiftBrandId` = '{0}'", input.ThirdPartyBrandId);
            }

            var connectionString = _appConfigurationAccessor.Configuration[$"ConnectionStrings:{SaaSConsts.ConnectionStringName}"];

            var parameters = new Dictionary<string, object>
            {
                { "@TenantId",  AbpSession.TenantId ?? 0 },
                { "@ParentCode", input.ParentCategoryCode}
            };

            var listGiftCategory = _sqlExecuteManager.Query<GiftCategory>(connectionString,
                @"SELECT `gc`.`Id`, `gc`.`Code`, `gc`.`Name`, `gc`.`Status`, `gc`.`Level`, `gc`.`ParentCode`, `gc`.`ParentId`, `gc`.`VendorId`, `gc`.`TargetAudienceId`, `gc`.`ThirdPartyGiftBrandId`
                                FROM `GiftCategory` AS `gc`
                                LEFT JOIN `TargetAudience` AS `ta` ON `ta`.`Id` = `gc`.`TargetAudienceId`
                                WHERE `gc`.`IsDeleted` <> true AND `gc`.`TenantId` = @TenantId AND `gc`.`ParentCode` = @ParentCode AND `gc`.`VendorId` IS NOT NULL"
                + queryVendorIdFilter
                + queryThirdPartyCategoryIdFilter
                + queryThirdPartyBrandIdFilter
                , parameters);

            if (listGiftCategory.Count() > 0)
            {
                foreach (var giftCategory in listGiftCategory)
                {
                    var output = new GetGiftCategoryForEditOutput { GiftCategory = ObjectMapper.Map<CreateOrEditGiftCategoryDto>(giftCategory) };

                    if (giftCategory.ParentId != null)
                    {
                        var giftParentCategory = await _giftCategoryRepository.FirstOrDefaultAsync((int)output.GiftCategory.ParentId);
                        output.GiftCategoryGiftCategoryName = giftParentCategory.Name.ToString();
                        output.GiftCategoryParentStatus = giftParentCategory.Status.ToString();

                        if (output.GiftCategoryParentStatus.Equals("I"))
                        {
                            continue;
                        }
                    }

                    if (giftCategory.VendorId != null)
                    {
                        var thirdPartyVendor = await _thirdPartyGiftVendorRepository.FirstOrDefaultAsync(x => x.Id == (int)output.GiftCategory.VendorId && x.Status == "A" && x.IsDeleted == false);
                        output.VendorStatus = "A";

                        if (thirdPartyVendor == null)
                        {
                            output.VendorStatus = "I";
                            continue;
                        }
                    }

                    // parameters.Add("@GiftCategoryCodeLst", listGiftCategory.Select(x => x.Code).Distinct().ToList());
                    var ThirdPartyCategoryCodeLst = listGiftCategory.Select(x => x.Code).Distinct().ToList();

                    parameters.Add("@TargetAudienceIds", listGiftCategory.Select(x => x.TargetAudienceId).Distinct().ToList());

                    var targetAudienceIdsLst = (IList)parameters["@TargetAudienceIds"];
                    if (targetAudienceIdsLst.Count > 0 && targetAudienceIdsLst[0] != null)
                    {
                        var targetAudienceList = _sqlExecuteManager.Query<SettingParamField>(connectionString,
                            @"SELECT `tgd`.`Code`, `tgd`.`Type`, `tgd`.`Value`, `tgd`.`Level`, `tgd`.`FullValue`, `tgd`.`IsActive`, `tgd`.`SegmentId`
                            FROM `TargetAudienceDetail` AS `tgd` 
                            WHERE `tgd`.`IsDeleted` <> true AND `tgd`.`TenantId` = @TenantId AND `tgd`.`TargetAudienceId` IN @TargetAudienceIds", parameters);
                        output.GiftCategory.TargetAudience.TargetAudienceDetailDtos = ObjectMapper.Map<ICollection<CreateOrEditSettingParamFieldDto>>(targetAudienceList);
                    }
                    else
                    {
                        parameters.Remove("@TargetAudienceIds");
                    }

                    string queryThirdPartyGiftCategory = string.Join("','", ThirdPartyCategoryCodeLst);
                    var thirdPartyGifts = _sqlExecuteManager.Query<GiftInfor>(connectionString,
                       @"SELECT `g`.`Id`, `g`.`Code`, `g`.`Name`, `g`.`Description`,  `g`.`Introduce`, `g`.`FullGiftCategoryCode`, `g`.`BrandName`, `g`.`BrandId`, `g`.`ThirdPartyBrandName`, `g`.`ThirdPartyBrandMappingId`, 
                                `g`.`ThirdPartyCategoryName`, `g`.`ThirdPartyCategoryId`, `g`.`Vendor`, `g`.`EffectiveFrom`, `g`.`EffectiveTo`, `g`.`RequiredCoin`, `g`.`Status`, `g`.`TotalQuantity`, `g`.`UsedQuantity`, 
                                `g`.`RemainingQuantity`, `g`.`IsEGift`, `g`.`IsAutoGeneratedEGiftCode`, `g`.`ExpiryDate`, `g`.`ExpiryDuration`, `g`.`TargetAudienceId`, `g`.`Tag`, `g`.`GiftGroupId`, `g`.`FullPrice`,
                                `g`.`DiscountPrice`, `g`.`VendorId`, `g`.`Is3rdPartyGift`, `g`.`IsPublish`, `g`.`Selected`, `g`.`ThirdPartyGiftCode`, `g`.`ThirdPartyCategoryCode`, `g`.`RegionCode`, 
                                `g`.`Disable`, `g`.`Office`, `g`.`ExpireDuration`, `g`.`TotalWish`, `g`.`IsDefaultMargin`, `g`.`Margin`, `g`.`VendorPrice`, `g`.`Note3rdPartyChange`, `g`.`UsageCheck`
                            FROM `Gift` AS `g` WHERE `g`.`Is3rdPartyGift` <> false AND `g`.`VendorId` = " + giftCategory.VendorId + "  AND `g`.`IsDeleted` <> true AND `g`.`TenantId` = @TenantId AND `g`.`ThirdPartyCategoryCode` IN ('" + queryThirdPartyGiftCategory + "')", parameters);

                    // parameters.Remove("@GiftCategoryCodeLst");
                    var listResult = new List<ThirdPartyGiftInfo>();

                    List<ThirdPartyGiftInfoForEdit> personViews = Mapper.Map<List<GiftInfor>, List<ThirdPartyGiftInfoForEdit>>(thirdPartyGifts);

                    var contractResolver = new DefaultContractResolver
                    {
                        NamingStrategy = new CamelCaseNamingStrategy()
                    };

                    var newGiftListJSON = JsonConvert.SerializeObject(personViews, new JsonSerializerSettings
                    {
                        ContractResolver = contractResolver,
                        Formatting = Formatting.Indented
                    });

                    output.GiftCategory.GiftListJSON = newGiftListJSON;
                    result.Add(output);
                }
            }

            /*if (listGiftCategory.Count > 0)
            {
                foreach (var giftCategory in listGiftCategory)
                {
                    var output = new GetGiftCategoryForEditOutput { GiftCategory = ObjectMapper.Map<CreateOrEditGiftCategoryDto>(giftCategory) };

                    parameters.Add("@ListGiftCategoryIds", listGiftCategory.Select(x => x.Code).Distinct().ToList());
                    parameters.Add("@ListVendorIds", listGiftCategory.Select(x => x.VendorId).Distinct().ToList());
                    parameters.Add("@TargetAudienceIds", listGiftCategory.Select(x => x.TargetAudienceId).Distinct().ToList());

                    var queryTargetAudienceFilter = string.Empty;
                    var collection = (IList)parameters["@TargetAudienceIds"];
                    if (collection.Count > 0 && collection[0] != null)
                    {
                        var targetAudienceList = _sqlExecuteManager.Query<SettingParamField>(connectionString,
                            @"SELECT `tgd`.`Code`, `tgd`.`Type`, `tgd`.`Value`, `tgd`.`Level`, `tgd`.`FullValue`, `tgd`.`IsActive`, `tgd`.`SegmentId`
                            FROM `TargetAudienceDetail` AS `tgd` 
                            WHERE `tgd`.`IsDeleted` <> true AND `tgd`.`TenantId` = @TenantId AND `tgd`.`TargetAudienceId` IN @TargetAudienceIds", parameters);
                        output.GiftCategory.TargetAudience.TargetAudienceDetailDtos = ObjectMapper.Map<ICollection<CreateOrEditSettingParamFieldDto>>(targetAudienceList);
                    }

                    if (output.GiftCategory.ParentId != null)
                    {
                        var giftParentCategory = await _giftCategoryRepository.FirstOrDefaultAsync((int)output.GiftCategory.ParentId);
                        output.GiftCategoryGiftCategoryName = giftParentCategory.Name.ToString();
                        output.GiftCategoryParentStatus = giftParentCategory.Status.ToString();

                        if (output.GiftCategoryParentStatus.Equals("I"))
                        {
                            // throw new UserFriendlyException(langMutil("ParentCategoryIsTemporaryInactive"));
                           continue;
                        }
                    }

                    if (output.GiftCategory.VendorId != null)
                    {
                        var thirdPartyVendor = await _thirdPartyGiftVendorRepository.FirstOrDefaultAsync((int)output.GiftCategory.VendorId);
                        output.VendorStatus = thirdPartyVendor.Status.ToString();

                        if (output.VendorStatus.Equals("I"))
                        {
                            // throw new UserFriendlyException(langMutil("VendorIsTemporaryInactive"));
                            continue;
                        }
                    }

                    string queryThirdPartyCategoryId = string.Empty;
                    string queryThirdPartyBrandId = string.Empty;

                    *//*if (!string.IsNullOrEmpty(giftCategory.ThirdPartyGiftCategoryId))
                    {
                        queryThirdPartyCategoryId = string.Format(" AND `g`.`ThirdPartyCategoryId` = {0}", giftCategory.ThirdPartyGiftCategoryId);
                    } else if (!string.IsNullOrEmpty(giftCategory.ThirdPartyGiftBrandId))
                    {
                        var thirdPartyBrand = await _thirdPartyBrandMapping.GetAll().Where(x => x.ThirdPartyBrandId == giftCategory.ThirdPartyGiftBrandId && x.VendorId == giftCategory.VendorId).ToListAsync();
                        var thirdPartyBrandMappingIds = thirdPartyBrand.Select(x => x.Id).ToList();
                        if (thirdPartyBrandMappingIds.IsAny()) {
                            queryThirdPartyBrandId = string.Format(" AND `g`.`ThirdPartyBrandMappingId` IN {0}", thirdPartyBrandMappingIds);
                        }
                    }*//*


                    var thirdPartyGifts = _sqlExecuteManager.Query<GiftInfor>(connectionString,
                        @"SELECT `g`.`Id`, `g`.`Code`, `g`.`Name`, `g`.`Description`,  `g`.`Introduce`, `g`.`FullGiftCategoryCode`, `g`.`BrandName`, `g`.`BrandId`, `g`.`ThirdPartyBrandName`, `g`.`ThirdPartyBrandMappingId`, 
                                `g`.`ThirdPartyCategoryName`, `g`.`ThirdPartyCategoryId`, `g`.`Vendor`, `g`.`EffectiveFrom`, `g`.`EffectiveTo`, `g`.`RequiredCoin`, `g`.`Status`, `g`.`TotalQuantity`, `g`.`UsedQuantity`, 
                                `g`.`RemainingQuantity`, `g`.`IsEGift`, `g`.`IsAutoGeneratedEGiftCode`, `g`.`ExpiryDate`, `g`.`ExpiryDuration`, `g`.`TargetAudienceId`, `g`.`Tag`, `g`.`GiftGroupId`, `g`.`FullPrice`,
                                `g`.`DiscountPrice`, `g`.`VendorId`, `g`.`Is3rdPartyGift`, `g`.`IsPublish`, `g`.`Selected`, `g`.`ThirdPartyGiftCode`, `g`.`ThirdPartyCategoryCode`, `g`.`RegionCode`, 
                                `g`.`Disable`, `g`.`Office`, `g`.`ExpireDuration`, `g`.`TotalWish`, `g`.`IsDefaultMargin`, `g`.`Margin`, `g`.`VendorPrice`, `g`.`Note3rdPartyChange`, `g`.`UsageCheck`
                            FROM `Gift` AS `g` WHERE `g`.`Is3rdPartyGift` <> false AND `g`.`VendorId` IN @  AND `g`.`IsDeleted` <> true AND `g`.`TenantId` = @TenantId AND `g`.`FullGiftCategoryCode` IN @ListGiftCategoryIds", parameters);

                    //Get Vendor Type: API || File Import
                    parameters.Add("VendorType", new List<int>(){ VendorTypeConsts.API.To<int>()});

                    var thirdPartyVendors = _sqlExecuteManager.Query<ThirdPartyGiftVendor>(connectionString,
                        @"SELECt `vdr`.`Id`, `vdr`.`VendorName`, `vdr`.`Status`, `vdr`.`CoinPriceExchangeRate`, `vdr`.`DefaultMargin`
                            FROM `ThirdPartyGiftVendor` AS `vdr` WHERE  `vdr`.`IsDeleted` <> true AND `vdr`.`TenantId` = @TenantId AND `vdr`.`VendorType` IN @VendorType", parameters
                        );


                    var roundType = GetRoundingType();
*//*                    var listResultFinal = (from g in thirdPartyGifts
                                           select new ThirdPartyGiftInfo
                                           ).ToList();*//*

                    var listResult = new List<ThirdPartyGiftInfo>();
                    if (thirdPartyGifts != null && thirdPartyGifts.Count > 0)
                    {
                        foreach (var gift in thirdPartyGifts)
                        {
                            var obj = ObjectMapper.Map<ThirdPartyGiftInfo>(gift);
                            obj.Code = gift.ThirdPartyGiftCode;
                            
                            //Auto-Map
                            //obj.VendorPrice = gift.VendorPrice;
                            listResult.Add(obj);
                        }

                        var contractResolver = new DefaultContractResolver
                        {
                            NamingStrategy = new CamelCaseNamingStrategy()
                        };
                        var newGiftListJSON = JsonConvert.SerializeObject(listResult, new JsonSerializerSettings
                        {
                            ContractResolver = contractResolver,
                            Formatting = Formatting.Indented
                        });

                        output.GiftCategory.GiftListJSON = newGiftListJSON;
                    }

                    result.Add(output);
                }
            }*/

            return result;
        }

        /*[AbpAuthorize(AppPermissions.Pages_GiftCategory_Edit)]
        public async Task GetThirdPartyGiftsForStore() {
            try
            {
                //Get Thirdparty gift categories
                var giftCategories = await _giftCategoryRepository.GetAll().Where(x => x.Status == "A" && x.Is3rdPartyGiftCategory == true && x.VendorId.HasValue)
                    .Include(t => t.Vendor).Where(x => x.Vendor.Status == "A" && x.IsDeleted == false && x.Is3rdPartyGiftCategory == true).ToListAsync();

                var vendorIds = giftCategories.Select(x => x.VendorId).Distinct().ToList();
                var vendors = await _thirdPartyGiftVendorRepository.GetAll().Where(x => vendorIds.Contains(x.Id)).ToListAsync();

                // Initialize adapter list based on vendor list
                var adapters = new Dictionary<int, IThirdPartyVendorAdapter>();
                foreach(var vendor in vendors) 
                { 
                    var adapter = ThirdPartyVendorAdapterFactory.GetAdapter(vendor.Type, ObjectMapper.Map<ThirdPartyVendorApiInfo>(vendor), Logger, _unitOfWorkManager, _clientInfoProvider, _auditLogRepository, _httpContextAccessor);
                    adapters.Add(vendor.Id, adapter);
                }

                var gifts = new List<ThirdPartyGiftInfo>();
                foreach (var giftCategory in giftCategories)
                {
                    if (!string.IsNullOrEmpty(giftCategory.ThirdPartyGiftCategoryId))
                    {
                        var response = await adapters[giftCategory.VendorId.Value].GetGiftCategory(new ThirdPartyGiftCategoryRequest
                        {
                            CategoryId = giftCategory.ThirdPartyGiftCategoryId ?? ""
                        }, giftCategory.TenantId);
                        if (!response.Success)
                        {
                            this.Logger.Error($"[GetThirdPartyGiftsForStore]___Get 3rdparty gifts by category throughs vendor id {giftCategory.VendorId.Value}___Unsuccess___{response.Message}");
                            //throw new UserFriendlyException($"Cannot get new third party gift list from {thirdPartyVendor.Type}.");
                        }

                        gifts = response.Gifts;
                    }
                    else if (!string.IsNullOrEmpty(giftCategory.ThirdPartyGiftBrandId))
                    {
                        var response = await adapters[giftCategory.VendorId.Value].GetGiftByListBrand(new ThirdPartyGiftByBrandRequest
                        {
                            BrandId = giftCategory.ThirdPartyGiftBrandId ?? ""
                        }, giftCategory.TenantId);

                        if (!response.Success)
                        {
                            this.Logger.Error($"[GetThirdPartyGiftsForStore]___Get 3rdparty gifts by brand throughs vendor id {giftCategory.VendorId.Value}___Unsuccess___{response.Message}");
                            //throw new UserFriendlyException($"Cannot get new third party gift list from {thirdPartyVendor.Type}.");
                        }
                        gifts = response.Gifts;
                    }

                    var oldGifts = await _giftInforRepository.GetAll().Where(e => e.Is3rdPartyGift
                                    //&& e.Selected
                                    && e.VendorId.Value == giftCategory.VendorId
                                    && e.ThirdPartyCategoryCode == giftCategory.Code
                                    //&& e.Status == "A"
                                    ).ToListAsync();

                    var roundType = GetRoundingType();
                    this.Logger.Info("***Start edit thirdPartyGiftVendor.");

                    var listResult = new List<ThirdPartyGiftInfo>();

                    // Get old selected Gift code & new selected Gift code
                    var oldSelectedCodes = oldGifts.Select(t => t.ThirdPartyGiftCode).ToList();
                    var newSelectedCodes = gifts.Select(t => t.Code).ToList();

                    // Find unselect gifts, create new gifts and update info gifts 
                    var needRemoveGiftCodes = oldSelectedCodes.Except(newSelectedCodes).ToList();
                    var needCreateGiftCodes = newSelectedCodes.Except(oldSelectedCodes).ToList();
                    var needUpdateGiftCodes = oldSelectedCodes.Intersect(newSelectedCodes).ToList();

                    var listVendorUsingImport = ThirdPartyGiftVendorUsingImportGift.VendorUsing;
                    var vendorHasUsingImport = listVendorUsingImport.Find(x => x == giftCategory.Vendor.Type);
                    using (var unitOfWork = _unitOfWorkManager.Begin())
                    {
                        // Set unselect gift
                        var needRemoveGifts = oldGifts.Where(t => needRemoveGiftCodes.Contains(t.ThirdPartyGiftCode)).ToList();
                        foreach (var item in needRemoveGifts)
                        {
                            var thirdPartyChangeHistory = ObjectMapper.Map<ThirdpartyChangeHistory>(item);
                            thirdPartyChangeHistory.ChangeVendorStatus = "Removed";
                            thirdPartyChangeHistory.DateOfGetting = DateTime.UtcNow;

                            item.ChangeStatus = "Removed";
                            await _giftInforRepository.UpdateAsync(item);
                        }

                        //New gifts info
                        *//*var needCreateGifts = gifts.Where(t => needCreateGiftCodes.Contains(t.Code)).ToList();
                        foreach (var gift in needCreateGifts)
                        {
                            var giftInfo = ObjectMapper.Map<GiftInfor>(gift);
                            //Dealtoday need update quantity for gift
                            giftInfo.Margin = (decimal)giftCategory.Vendor.DefaultMargin;
                            giftInfo.SellOutPrice = RoundExtensions.RoundByType(gift.SellInPrice * (1 + gift.Margin / 100), roundType);
                            giftInfo.ThirdPartyGiftBrandId = giftCategory.ThirdPartyGiftBrandId;
                            giftInfo.ThirdPartyGiftCategoryId = giftCategory.ThirdPartyGiftCategoryId;
                            giftInfo.VendorName = giftCategory.Vendor.VendorName;
                            giftInfo.VendorId = giftCategory.VendorId;
                            giftInfo.ThirdPartyCategoryId = giftCategory.Id;
                            giftInfo.TotalQuantity = 0;
                            giftInfo.RemainingQuantity = 0;
                            if (giftCategory.Vendor.Type == "Dealtoday")
                            {
                                giftInfo.UsageCheck = true;
                            }
                            giftInfo.ChangeStatus = "New";

                            await _giftInforRepository.(item);
                        }

                        await unitOfWork.CompleteAsync();*//*
                    }


                    this.Logger.Info("***End edit thirdPartyGiftVendor.");
                }
            }
            catch (Exception ex) 
            {
                Logger.Error($"GetThirdPartyGiftsForStore___Exception: \n{0}", ex);
            }
        }*/

        [AbpAuthorize(AppPermissions.Pages_GiftCategory_Edit)]
        public async Task<GetGiftCategoryForEditOutput> GetGiftCategoryForEdit(EntityDto input)
        {
            var giftCategory = await _giftCategoryRepository.GetAll().Include(e => e.TargetAudience).ThenInclude(e => e.TargetAudienceDetail)
                .FirstOrDefaultAsync(e => e.Id == input.Id);

            var output = new GetGiftCategoryForEditOutput { GiftCategory = ObjectMapper.Map<CreateOrEditGiftCategoryDto>(giftCategory) };
            output.IsCategoryClone = giftCategory.IsCategoryClone;
            // target audience
            if (giftCategory.TargetAudience != null && giftCategory.TargetAudience.TargetAudienceDetail != null)
            {
                output.GiftCategory.TargetAudience.TargetAudienceDetailDtos = ObjectMapper.Map<ICollection<CreateOrEditSettingParamFieldDto>>(giftCategory.TargetAudience.TargetAudienceDetail);
            }

            if (output.GiftCategory.ParentId != null)
            {
                var giftParentCategory = await _giftCategoryRepository.FirstOrDefaultAsync((int)output.GiftCategory.ParentId);
                output.GiftCategoryGiftCategoryName = giftParentCategory.Name.ToString();
                output.GiftCategoryParentStatus = giftParentCategory.Status.ToString();

                if (output.GiftCategoryParentStatus.Equals("I"))
                {
                    throw new UserFriendlyException(langMutil("ParentCategoryIsTemporaryInactive"));
                }
            }

            if (output.GiftCategory.VendorId != null)
            {
                var thirdPartyVendor = await _thirdPartyGiftVendorRepository.FirstOrDefaultAsync((int)output.GiftCategory.VendorId);
                output.VendorStatus = thirdPartyVendor.Status.ToString();

                if (output.VendorStatus.Equals("I"))
                {
                    throw new UserFriendlyException(langMutil("VendorIsTemporaryInactive"));
                }

                var oldGifts = await _giftInforRepository.GetAll().Where(e => e.Is3rdPartyGift
                            //&& e.Selected
                            && e.VendorId.Value == giftCategory.VendorId
                            && e.ThirdPartyCategoryCode == giftCategory.Code
                            //&& e.Status == "A"
                            ).ToListAsync();

                //var adapter = ThirdPartyVendorAdapterFactory.GetAdapter(thirdPartyVendor.Type, ObjectMapper.Map<ThirdPartyVendorApiInfo>(thirdPartyVendor), Logger);
                var adapter = ThirdPartyVendorAdapterFactory.GetAdapter(thirdPartyVendor.Type, ObjectMapper.Map<ThirdPartyVendorApiInfo>(thirdPartyVendor), Logger, _unitOfWorkManager, _clientInfoProvider, _auditLogRepository, _httpContextAccessor);
                var gifts = new List<ThirdPartyGiftInfo>();
                if (!string.IsNullOrEmpty(giftCategory.ThirdPartyGiftCategoryId))
                {
                    var response = await adapter.GetGiftCategory(new ThirdPartyGiftCategoryRequest
                    {
                        CategoryId = giftCategory.ThirdPartyGiftCategoryId ?? ""
                    }, thirdPartyVendor.TenantId);
                    if (!response.Success)
                    {
                        this.Logger.Error(response.Message);
                        throw new UserFriendlyException($"Cannot get new third party gift list from {thirdPartyVendor.Type}.");
                    }
                    gifts = response.Gifts;
                }
                else if (!string.IsNullOrEmpty(giftCategory.ThirdPartyGiftBrandId))
                {
                    var response = await adapter.GetGiftByListBrand(new ThirdPartyGiftByBrandRequest
                    {
                        BrandId = giftCategory.ThirdPartyGiftBrandId ?? ""
                    }, thirdPartyVendor.TenantId);
                    if (!response.Success)
                    {
                        this.Logger.Error(response.Message);
                        throw new UserFriendlyException($"Cannot get new third party gift list from {thirdPartyVendor.Type}.");
                    }
                    gifts = response.Gifts;
                }
                var roundType = GetRoundingType();
                this.Logger.Info("***Start edit thirdPartyGiftVendor.");

                var listResult = new List<ThirdPartyGiftInfo>();

                // Get old selected Gift code & new selected Gift code
                var oldSelectedCodes = oldGifts.Select(t => t.ThirdPartyGiftCode).ToList();
                var newSelectedCodes = gifts.Where(t => t.Status == "A").Select(t => t.Code).ToList();

                // Find unselect gifts, create new gifts and update info gifts 
                var needRemoveGiftCodes = oldSelectedCodes.Except(newSelectedCodes).ToList();
                var needCreateGiftCodes = newSelectedCodes.Except(oldSelectedCodes).ToList();
                var needUpdateGiftCodes = oldSelectedCodes.Intersect(newSelectedCodes).ToList();

                // Set unselect gift
                var needRemoveGifts = oldGifts.Where(t => needRemoveGiftCodes.Contains(t.ThirdPartyGiftCode)).ToList();
                var listVendorUsingImport = ThirdPartyGiftVendorUsingImportGift.VendorUsing;
                var vendorHasUsingImport = listVendorUsingImport.Find(x => x == thirdPartyVendor.Type);
                foreach (var item in needRemoveGifts)
                {
                    var obj = ObjectMapper.Map<ThirdPartyGiftInfo>(item);
                    obj.Code = item.ThirdPartyGiftCode;
                    obj.SellInPrice = obj.VendorPrice / (decimal)thirdPartyVendor.CoinPriceExchangeRate;
                    obj.SellOutPrice = RoundExtensions.RoundByType(obj.SellInPrice * (1 + obj.Margin / 100), roundType);
                    if (vendorHasUsingImport == null)
                    {
                        obj.Selected = false;
                        obj.Note = $"Gift was removed or disabled.";
                        obj.IsChanged = !string.IsNullOrEmpty(obj.Note);
                    }
                    obj.ThirdPartyGiftBrandId = giftCategory.ThirdPartyGiftBrandId;
                    obj.ThirdPartyGiftCategoryId = giftCategory.ThirdPartyGiftCategoryId;
                    obj.VendorName = thirdPartyVendor.VendorName;
                    obj.ThirdPartyCategoryId = giftCategory.Id;
                    obj.VendorId = giftCategory.VendorId;
                    obj.ChangeStatus = "Removed";

                    listResult.Add(obj);
                }


                //needUnselectedGifts.ForEach(t => t.Selected = false);

                //var publishGift = new List<GiftInfor>();

                //Update gifts info
                var needUpdateGifts = gifts.Where(t => needUpdateGiftCodes.Contains(t.Code)).ToList();
                foreach (var gift in needUpdateGifts)
                {
                    var flagChanged = false;
                    var oldGift = oldGifts.FirstOrDefault(t => t.ThirdPartyGiftCode == gift.Code);

                    gift.Selected = oldGift?.Selected ?? false;
                    gift.Margin = (decimal)thirdPartyVendor.DefaultMargin;

                    gift.Note = oldGift?.Note3rdPartyChange;
                    gift.IsChanged = !string.IsNullOrEmpty(gift.Note);

                    // Apply manual margin that user inputs
                    if (oldGift != null)
                    {
                        if (!oldGift.IsDefaultMargin)
                        {
                            gift.IsDefaultMargin = oldGift.IsDefaultMargin;
                            gift.Margin = oldGift.Margin;
                        }
                        // check vendor price
                        var noteChange = string.Empty;
                        if (oldGift.VendorPrice != gift.VendorPrice && oldGift.Selected)
                        {
                            gift.VendorPrice = oldGift.VendorPrice;
                            noteChange += $" | Vendor Price was changed";
                            if (thirdPartyVendor?.CoinPriceExchangeRate != 0)
                            {
                                gift.SellInPrice = gift.VendorPrice / (decimal)thirdPartyVendor?.CoinPriceExchangeRate;
                            }
                            else
                            {
                                gift.SellInPrice = 0;
                            }

                        }
                        if (thirdPartyVendor.Type == "Urbox")
                        {
                            gift.EffectiveFrom = oldGift?.EffectiveFrom ?? DateTime.UtcNow;
                            gift.EffectiveTo = oldGift?.EffectiveTo ?? DateTime.UtcNow.AddYears(10);
                        }
                        else if (thirdPartyVendor.Type == "Dealtoday")
                        {
                            if (oldGift.EffectiveFrom != gift.EffectiveFrom && oldGift.Selected)
                            {
                                noteChange += $" | Vendor Effective from was changed";
                                gift.EffectiveFromNew = gift.EffectiveFrom;
                                gift.EffectiveFrom = oldGift.EffectiveFrom;
                                gift.ChangeStatus = "Change Effective From";
                            }
                            if (oldGift.EffectiveTo != gift.EffectiveTo && oldGift.Selected)
                            {
                                noteChange += $" | Vendor Effective to was changed";
                                gift.EffectiveToNew = gift.EffectiveFrom;
                                gift.EffectiveTo = oldGift.EffectiveTo;
                                gift.ChangeStatus = "Change Effective To";
                            }
                        }
                        if (!string.IsNullOrWhiteSpace(noteChange))
                        {
                            gift.Note = noteChange.TrimStart().TrimStart('|');
                            gift.IsChanged = !string.IsNullOrEmpty(gift.Note);
                            // gift.Selected = false;
                            flagChanged = true;
                        }

                        gift.TotalQuantity = oldGift.TotalQuantity;
                        gift.RemainingQuantity = oldGift.RemainingQuantity;
                    }

                    if (oldGift.VendorPrice != gift.SellInPrice && oldGift.Selected)
                    {
                        gift.SellInPriceNew = gift.SellInPrice;
                        gift.SellInPrice = oldGift.VendorPrice;
                        gift.ChangeStatus = "Change Sell In Price";
                    }

                    gift.SellOutPrice = RoundExtensions.RoundByType(gift.SellInPrice * (1 + gift.Margin / 100), roundType);
                    gift.LastModificationTime = oldGift?.LastModificationTime ?? DateTime.UtcNow;
                    gift.ThirdPartyGiftBrandId = giftCategory.ThirdPartyGiftBrandId;
                    gift.ThirdPartyGiftCategoryId = giftCategory.ThirdPartyGiftCategoryId;
                    gift.VendorId = giftCategory.VendorId;
                    gift.VendorName = thirdPartyVendor.VendorName;
                    gift.ThirdPartyCategoryId = giftCategory.Id;
                    if (thirdPartyVendor.Type == "Dealtoday")
                    {
                        gift.UsageCheck = true;
                    }

                    if (flagChanged == true) { gift.ChangeStatus = "Changed"; }
                    else { gift.ChangeStatus = "UpToDate"; }

                    listResult.Add(gift);
                }

                //New gifts info
                var needCreateGifts = gifts.Where(t => needCreateGiftCodes.Contains(t.Code)).ToList();
                foreach (var gift in needCreateGifts)
                {
                    //Dealtoday need update quantity for gift
                    gift.Margin = (decimal)thirdPartyVendor.DefaultMargin;
                    gift.SellOutPrice = RoundExtensions.RoundByType(gift.SellInPrice * (1 + gift.Margin / 100), roundType);
                    gift.ThirdPartyGiftBrandId = giftCategory.ThirdPartyGiftBrandId;
                    gift.ThirdPartyGiftCategoryId = giftCategory.ThirdPartyGiftCategoryId;
                    gift.VendorName = thirdPartyVendor.VendorName;
                    gift.VendorId = giftCategory.VendorId;
                    gift.ThirdPartyCategoryId = giftCategory.Id;
                    gift.TotalQuantity = 0;
                    gift.RemainingQuantity = 0;
                    if (thirdPartyVendor.Type == "Dealtoday")
                    {
                        gift.UsageCheck = true;
                    }
                    gift.ChangeStatus = "New";

                    listResult.Add(gift);
                }

                this.Logger.Info("***End edit thirdPartyGiftVendor.");

                var contractResolver = new DefaultContractResolver
                {
                    NamingStrategy = new CamelCaseNamingStrategy()
                };
                var newGiftListJSON = JsonConvert.SerializeObject(listResult, new JsonSerializerSettings
                {
                    ContractResolver = contractResolver,
                    Formatting = Formatting.Indented
                });

                output.GiftCategory.GiftListJSON = newGiftListJSON;

            }

            output.GiftCategory.ImageLink = _imageLinkRepository.FirstOrDefault(p => p.Code == output.GiftCategory.Code && p.Type.Equals("GiftCategory"))?.FullLink;


            return output;
        }

        [AbpAuthorize(AppPermissions.Pages_GiftCategory_Edit)]
        public async Task<GetGiftCategoryForEditOutput> GetMultipleGiftCategories(EntityDto input, ThirdPartyGetFetchMultipleGiftCategoriesForEditInput inputFilter)
        {
            var giftCategory = await _giftCategoryRepository.GetAll()
            .Include(e => e.TargetAudience)
            .ThenInclude(e => e.TargetAudienceDetail)
            .FirstOrDefaultAsync(e => e.Id == input.Id && e.Is3rdPartyGiftCategory == true);

            var output = new GetGiftCategoryForEditOutput { GiftCategory = ObjectMapper.Map<CreateOrEditGiftCategoryDto>(giftCategory) };

            try
            {

                if (giftCategory == null)
                {
                    return new GetGiftCategoryForEditOutput();
                }

                // target audience
                if (giftCategory.TargetAudience != null && giftCategory.TargetAudience.TargetAudienceDetail != null)
                {
                    output.GiftCategory.TargetAudience.TargetAudienceDetailDtos = ObjectMapper.Map<ICollection<CreateOrEditSettingParamFieldDto>>(giftCategory.TargetAudience.TargetAudienceDetail);
                }

                if (output.GiftCategory.ParentId != null)
                {
                    var giftParentCategory = await _giftCategoryRepository.FirstOrDefaultAsync((int)output.GiftCategory.ParentId);
                    output.GiftCategoryGiftCategoryName = giftParentCategory.Name.ToString();
                    output.GiftCategoryParentStatus = giftParentCategory.Status.ToString();

                    if (output.GiftCategoryParentStatus.Equals("I"))
                    {
                        return output;
                        //throw new UserFriendlyException(langMutil("ParentCategoryIsTemporaryInactive"));
                    }
                }

                if (output.GiftCategory.VendorId != null)
                {
                    var thirdPartyVendor = await _thirdPartyGiftVendorRepository.FirstOrDefaultAsync((int)output.GiftCategory.VendorId);
                    if (thirdPartyVendor == null)
                    {
                        return output;
                    }
                    output.VendorStatus = thirdPartyVendor?.Status?.ToString() ?? "I";

                    if (output.VendorStatus.Equals("I"))
                    {
                        return output;
                        //throw new UserFriendlyException(langMutil("VendorIsTemporaryInactive"));
                    }

                    var oldGifts = await _giftInforRepository.GetAll().Where(e => e.Is3rdPartyGift
                                //&& e.Selected
                                && e.VendorId.Value == giftCategory.VendorId
                                && e.ThirdPartyCategoryCode == giftCategory.Code
                                //&& e.Status == "A"
                                ).ToListAsync();

                    //var adapter = ThirdPartyVendorAdapterFactory.GetAdapter(thirdPartyVendor.Type, ObjectMapper.Map<ThirdPartyVendorApiInfo>(thirdPartyVendor), Logger);
                    var gifts = new List<ThirdPartyGiftInfo>();


                    var adapter = ThirdPartyVendorAdapterFactory.GetAdapter(thirdPartyVendor.Type, ObjectMapper.Map<ThirdPartyVendorApiInfo>(thirdPartyVendor), Logger, _unitOfWorkManager, _clientInfoProvider, _auditLogRepository, _httpContextAccessor);

                    if (!string.IsNullOrEmpty(giftCategory.ThirdPartyGiftCategoryId))
                    {
                        var response = await adapter.GetGiftCategory(new ThirdPartyGiftCategoryRequest
                        {
                            CategoryId = giftCategory.ThirdPartyGiftCategoryId ?? ""
                        }, thirdPartyVendor.TenantId);
                        if (!response.Success)
                        {
                            this.Logger.Error(response.Message);
                            //throw new UserFriendlyException($"Cannot get new third party gift list from {thirdPartyVendor.Type}.");
                        }
                        gifts = response.Gifts;
                    }
                    else if (!string.IsNullOrEmpty(giftCategory.ThirdPartyGiftBrandId))
                    {
                        var response = await adapter.GetGiftByListBrand(new ThirdPartyGiftByBrandRequest
                        {
                            BrandId = giftCategory.ThirdPartyGiftBrandId ?? ""
                        }, thirdPartyVendor.TenantId);
                        if (!response.Success)
                        {
                            this.Logger.Error(response.Message);

                            //throw new UserFriendlyException($"Cannot get new third party gift list from {thirdPartyVendor.Type}.");
                        }
                        gifts = response.Gifts;
                    }


                    var roundType = GetRoundingType();
                    Logger.Info("***Start edit thirdPartyGiftVendor.");

                    if (gifts == null || !gifts.IsAny())
                    {
                        Logger.Info($"[GetMultipleGiftCategories]___Gifts response from 3rd is Empty");
                        return output;
                    }

                    Logger.Info($"[GetMultipleGiftCategories]___Gifts response from 3rd: {JsonConvert.SerializeObject(gifts)}");
                    var listResult = new List<ThirdPartyGiftInfo>();

                    // Get old selected Gift code & new selected Gift code
                    var oldSelectedCodes = oldGifts.Select(t => t.ThirdPartyGiftCode).ToList();
                    var newSelectedCodes = gifts.Where(t => t.Status == "A").Select(t => t.Code).ToList();

                    // Find unselect gifts, create new gifts and update info gifts 
                    var needRemoveGiftCodes = oldSelectedCodes.Except(newSelectedCodes).ToList();
                    var needCreateGiftCodes = newSelectedCodes.Except(oldSelectedCodes).ToList();
                    var needUpdateGiftCodes = oldSelectedCodes.Intersect(newSelectedCodes).ToList();

                    // Set unselect gift
                    var needRemoveGifts = oldGifts.Where(t => needRemoveGiftCodes.Contains(t.ThirdPartyGiftCode)).ToList();
                    var listVendorUsingImport = ThirdPartyGiftVendorUsingImportGift.VendorUsing;
                    var vendorHasUsingImport = listVendorUsingImport.Find(x => x == thirdPartyVendor.Type);
                    var changeStatusContent = new List<string>();

                    foreach (var item in needRemoveGifts)
                    {
                        var obj = ObjectMapper.Map<ThirdPartyGiftInfo>(item);
                        obj.Code = item.ThirdPartyGiftCode;
                        obj.SellInPrice = obj.VendorPrice / (decimal)thirdPartyVendor.CoinPriceExchangeRate;
                        obj.SellOutPrice = RoundExtensions.RoundByType(obj.SellInPrice * (1 + obj.Margin / 100), roundType);
                        if (vendorHasUsingImport == null)
                        {
                            obj.Selected = false;
                            obj.Note = $"Gift was removed or disabled.";
                            obj.IsChanged = !string.IsNullOrEmpty(obj.Note);
                        }
                        obj.ThirdPartyGiftBrandId = giftCategory.ThirdPartyGiftBrandId;
                        obj.ThirdPartyGiftCategoryId = giftCategory.ThirdPartyGiftCategoryId;
                        obj.VendorName = thirdPartyVendor.VendorName;
                        obj.ThirdPartyCategoryId = giftCategory.Id;
                        obj.VendorId = giftCategory.VendorId;
                        obj.ChangeStatus = "Removed";
                        obj.Priority = 3;
                        obj.Status = "I";

                        await _giftInforRepository.UpdateAsync(item.Id, t =>
                        {
                            t.Selected = false;
                            t.Status = "I";
                            return Task.FromResult(0);
                        });

                        listResult.Add(obj);
                    }
                    //needUnselectedGifts.ForEach(t => t.Selected = false);

                    //var publishGift = new List<GiftInfor>();

                    //Update gifts info
                    var needUpdateGifts = gifts.Where(t => needUpdateGiftCodes.Contains(t.Code)).ToList();
                    foreach (var gift in needUpdateGifts)
                    {
                        changeStatusContent.Clear();
                        var oldGift = oldGifts.FirstOrDefault(t => t.ThirdPartyGiftCode == gift.Code);
                        // gift.Selected = oldGift?.Selected ?? false;
                        gift.Margin = (decimal)thirdPartyVendor.DefaultMargin;
                        gift.Status = oldGift.Status;
                        gift.Note = oldGift?.Note3rdPartyChange;
                        gift.IsChanged = !string.IsNullOrEmpty(gift.Note);
                        gift.FieldToCal = gift.SellInPrice;

                        // Apply manual margin that user inputs
                        if (oldGift != null)
                        {
                            if (!oldGift.IsDefaultMargin)
                            {
                                gift.IsDefaultMargin = oldGift.IsDefaultMargin;
                                gift.Margin = oldGift.Margin;
                            }
                            // check vendor price
                            var noteChange = string.Empty;
                            if (oldGift.VendorPrice != gift.VendorPrice && oldGift.Selected)
                            {
                                // gift.VendorPrice = oldGift.VendorPrice;
                                noteChange += $" | Vendor Price was changed";

                                if (thirdPartyVendor?.CoinPriceExchangeRate != 0)
                                {
                                    gift.SellInPrice = oldGift.VendorPrice / (decimal)thirdPartyVendor?.CoinPriceExchangeRate;
                                    gift.SellInPriceNew = gift.VendorPrice / (decimal)thirdPartyVendor?.CoinPriceExchangeRate;
                                }
                                else
                                {
                                    gift.SellInPrice = 0;
                                    gift.SellInPriceNew = 0;
                                }

                                gift.FieldToCal = gift.SellInPriceNew;
                                changeStatusContent.Add("Change Sell In Price");
                                gift.Priority = 1;
                            }
                            if (thirdPartyVendor.Type == "Urbox")
                            {
                                gift.EffectiveFrom = oldGift?.EffectiveFrom ?? DateTime.UtcNow;
                                gift.EffectiveTo = oldGift?.EffectiveTo ?? DateTime.UtcNow.AddYears(10);
                            }
                            else if (thirdPartyVendor.Type == "Dealtoday")
                            {
                                if (oldGift.EffectiveFrom != gift.EffectiveFrom && oldGift.Selected)
                                {
                                    noteChange += $" | Vendor Effective from was changed";
                                    changeStatusContent.Add("Change Effective From");
                                    if (gift.Priority != 1)
                                    {
                                        gift.Priority = 2;
                                    }

                                    gift.EffectiveFromNew = gift.EffectiveFrom;
                                    gift.EffectiveFrom = oldGift.EffectiveFrom;
                                }
                                if (oldGift.EffectiveTo != gift.EffectiveTo && oldGift.Selected)
                                {
                                    noteChange += $" | Vendor Effective to was changed";
                                    changeStatusContent.Add("Change Effective To");
                                    if (gift.Priority != 1)
                                    {
                                        gift.Priority = 2;
                                    }

                                    gift.EffectiveToNew = gift.EffectiveTo;
                                    gift.EffectiveTo = oldGift.EffectiveTo;
                                }
                            }
                            if (!string.IsNullOrWhiteSpace(noteChange))
                            {
                                gift.Note = noteChange.TrimStart().TrimStart('|');
                                gift.IsChanged = !string.IsNullOrEmpty(gift.Note);
                                gift.Selected = true;

                                /*if (flagChangePrice)
                                {
                                    gift.Selected = false;
                                    *//*****************************************************
                                     * Update 3rdParty Gift with selected = false in case:
                                    - Changed price 

                                    - For Dealtoday, Effective From/To was changed
                                    - Remove Gift
                                     *****************************************************//*
                                    await _giftInforRepository.UpdateAsync(oldGift.Id, t =>
                                    {
                                        t.Selected = false;
                                        return Task.FromResult(0);
                                    });
                                }*/
                            }

                            gift.TotalQuantity = oldGift.TotalQuantity;
                            gift.RemainingQuantity = oldGift.RemainingQuantity;
                        }

                        gift.SellOutPrice = oldGift.RequiredCoin;
                        if (gift.SellInPriceNew.HasValue)
                        {
                            gift.SellOutPriceNew = RoundExtensions.RoundByType((gift.SellInPriceNew.Value) * (1 + gift.Margin / 100), roundType);
                        }
                        gift.LastModificationTime = oldGift?.LastModificationTime ?? DateTime.UtcNow;
                        gift.ThirdPartyGiftBrandId = giftCategory.ThirdPartyGiftBrandId;
                        gift.ThirdPartyGiftCategoryId = giftCategory.ThirdPartyGiftCategoryId;
                        gift.VendorId = giftCategory.VendorId;
                        gift.VendorName = thirdPartyVendor.VendorName;
                        gift.ThirdPartyCategoryId = giftCategory.Id;
                        gift.Status = oldGift.Status;
                        if (thirdPartyVendor.Type == "Dealtoday")
                        {
                            gift.UsageCheck = true;
                        }

                        if (changeStatusContent.FirstOrDefault() == null)
                        {
                            changeStatusContent.Add("No Change");
                            gift.Priority = 4;
                        }
                        gift.ChangeStatus = changeStatusContent.JoinAsString(" - ");
                        gift.Selected = true;

                        listResult.Add(gift);
                    }

                    //New gifts info
                    var needCreateGifts = gifts.Where(t => needCreateGiftCodes.Contains(t.Code)).ToList();
                    foreach (var gift in needCreateGifts)
                    {
                        //Dealtoday need update quantity for gift
                        gift.Margin = (decimal)thirdPartyVendor.DefaultMargin;
                        gift.SellOutPrice = RoundExtensions.RoundByType(gift.SellInPrice * (1 + gift.Margin / 100), roundType);
                        gift.ThirdPartyGiftBrandId = giftCategory.ThirdPartyGiftBrandId;
                        gift.ThirdPartyGiftCategoryId = giftCategory.ThirdPartyGiftCategoryId;
                        gift.VendorName = thirdPartyVendor.VendorName;
                        gift.VendorId = giftCategory.VendorId;
                        gift.ThirdPartyCategoryId = giftCategory.Id;
                        gift.TotalQuantity = 0;
                        gift.RemainingQuantity = 0;
                        if (thirdPartyVendor.Type == "Dealtoday")
                        {
                            gift.UsageCheck = true;
                        }
                        gift.ChangeStatus = "New";
                        gift.Priority = 5;
                        gift.Selected = true;
                        gift.FieldToCal = gift.SellInPrice;

                        listResult.Add(gift);
                    }

                    this.Logger.Info("***End edit thirdPartyGiftVendor.");

                    var contractResolver = new DefaultContractResolver
                    {
                        NamingStrategy = new CamelCaseNamingStrategy()
                    };

                    /* var statusFilter = string.Empty;
                     if (!string.IsNullOrEmpty(inputFilter.ChangeStatus)) 
                     {
                         switch (inputFilter.ChangeStatus)
                         {
                             case "New":
                                 priorityFilter = 5;
                                 break;
                             case "ChangeEffective":
                                 priorityFilter = 2;
                                 break;
                             case "NoChange":
                                 priorityFilter = 4;
                                 break;
                             case "Removed":
                                 priorityFilter = 3;
                                 break;
                             case "ChangePrice":
                                 priorityFilter = 1;
                                 break;
                             default:
                                 priorityFilter = 0;
                                 break;
                         }
                     }*/

                    if (!string.IsNullOrEmpty(inputFilter.Status) && !string.IsNullOrEmpty(inputFilter.ChangeStatus))
                    {
                        listResult = listResult.Where(x => x.Status == inputFilter.Status && x.ChangeStatus.Contains(inputFilter.ChangeStatus)).ToList();
                    }
                    else
                    {
                        //Filter: ChangeStatus
                        if (!string.IsNullOrEmpty(inputFilter.ChangeStatus))
                        {
                            listResult = listResult.Where(x => x.ChangeStatus.Contains(inputFilter.ChangeStatus)).ToList();
                        }

                        //Filter: Status
                        if (!string.IsNullOrEmpty(inputFilter.Status))
                        {
                            listResult = listResult.Where(x => x.Status == inputFilter.Status).ToList();
                        }
                    }

                    var newGiftListJSON = JsonConvert.SerializeObject(listResult, new JsonSerializerSettings
                    {
                        ContractResolver = contractResolver,
                        Formatting = Formatting.Indented
                    });


                    output.GiftCategory.GiftListJSON = newGiftListJSON;

                }

                output.GiftCategory.ImageLink = _imageLinkRepository.FirstOrDefault(p => p.Code == output.GiftCategory.Code && p.Type.Equals("GiftCategory"))?.FullLink;


                return output;
            }
            catch (Exception ex)
            {
                Logger.Info($"[GetMultipleGiftCategories]___Gifts response from API: GetGiftByCategory___Exception: {ex.Message} _ {ex.StackTrace}");
                return output;
            }
        }

        private async Task DeleteListFromCategory(List<string> code)
        {
            var giftInfor = _giftInforRepository.GetAll().Where(p => code.Contains(p.FullGiftCategoryCode)).ToList();
            giftInfor.ForEach(async p =>
            {
                p.FullGiftCategoryCode = null;
                await _giftInforRepository.UpdateAsync(p);
            });
            await CurrentUnitOfWork.SaveChangesAsync();

        }

        private async Task DeleteListGiftThirdParty(EntityDto input)
        {
            var category = _giftCategoryRepository.FirstOrDefault(p => p.Id == input.Id);
            if (category != null)
            {
                await _giftCategoryRepository.DeleteAsync(category);
                await DeleteListGiftThirdPartyFromCategory(category.Code);
            }
        }

        private async Task DeleteListGiftThirdPartyFromCategory(string code)
        {
            var giftInfor = _giftInforRepository.GetAll().Where(p => p.ThirdPartyCategoryCode == code).ToList();
            giftInfor.ForEach(async p =>
            {
                p.FullGiftCategoryCode = null;
                p.ThirdPartyCategoryCode = null;
                p.Status = "I";
                await _giftInforRepository.UpdateAsync(p);
            });
            await CurrentUnitOfWork.SaveChangesAsync();

        }

        private bool CheckCategoryName(CustomizeGiftInfosDto customizeGiftInfosDto)
        {
            if (customizeGiftInfosDto.Vendor != null && customizeGiftInfosDto.VendorType == VendorTypeConsts.API.ToString())
            {
                return !string.IsNullOrEmpty(customizeGiftInfosDto.CategoryName);
            }
            return true;
        }

        private List<GiftCategory> getChildItem(int? parentId)
        {
            List<GiftCategory> giftCategoryDtos = listGiftCategory.AsQueryable()
                .Where(e => e.ParentId == parentId).ToList();

            foreach (GiftCategory child in giftCategoryDtos.ToList())
            {
                List<GiftCategory> childs = getChildItem(child.Id);
                giftCategoryDtos.AddRange(childs);
            }

            return giftCategoryDtos;
        }
        //chua test
        private GiftCategory getParentItem(int? id)
        {
            var giftCategoryDtos = new GiftCategory();

            giftCategoryDtos = listGiftCategory.FirstOrDefault(p => p.Id == id);
            if (giftCategoryDtos != null && giftCategoryDtos.Level != 0)
            {
                giftCategoryDtos = getParentItem(giftCategoryDtos.Id);
            }

            return giftCategoryDtos;
        }

        private void InitListGiftCategory()
        {
            listGiftCategory = _giftCategoryRepository.GetAll()
                // .Where(p => p.Status.ToLower().Equals("a"))
                //.Select(x => ObjectMapper.Map<GiftCategoryDto>(x))
                .ToList();
        }

        private string langMutil(string keyWord)
        {
            return LocalizationManager.GetString("Akapoint", keyWord);
        }

        #region Third Party Gift Category

        public async Task<GetThirdPartyGiftsOutput> GetThirdPartyGifts(GetThirdPartyGiftsInput input)
        {
            var thirdPartyCategory = _giftCategoryRepository.GetAll()
                .Include(t => t.Vendor)
                .FirstOrDefault(t => t.Id == input.CategoryId && t.Is3rdPartyGiftCategory);

            if (thirdPartyCategory == null || thirdPartyCategory.VendorId == null)
                return new GetThirdPartyGiftsOutput();

            //var oldGifts = new List<ThirdPartyGiftInfo>();

            //if (!string.IsNullOrEmpty(thirdPartyCategory.GiftListJSON))
            //{
            //    try
            //    {
            //        // try parse old
            //        oldGifts = JsonConvert.DeserializeObject<List<ThirdPartyGiftInfo>>(thirdPartyCategory.GiftListJSON);
            //    }
            //    catch
            //    { }
            //}
            var oldGifts = _giftInforRepository.GetAll().Where(e => e.Is3rdPartyGift
                                    && e.VendorId.Value == thirdPartyCategory.VendorId
                                    && e.ThirdPartyCategoryCode == thirdPartyCategory.Code).ToList();

            // var adapter = ThirdPartyVendorAdapterFactory.GetAdapter(thirdPartyCategory.Vendor.Type, ObjectMapper.Map<ThirdPartyVendorApiInfo>(thirdPartyCategory.Vendor), Logger);
            var adapter = ThirdPartyVendorAdapterFactory.GetAdapter(thirdPartyCategory.Vendor.Type, ObjectMapper.Map<ThirdPartyVendorApiInfo>(thirdPartyCategory.Vendor), Logger, _unitOfWorkManager, _clientInfoProvider, _auditLogRepository, _httpContextAccessor);
            var response = await adapter.GetGiftCategory(new ThirdPartyGiftCategoryRequest
            {
                CategoryId = thirdPartyCategory.ThirdPartyGiftCategoryId ?? ""
            }, thirdPartyCategory.TenantId);

            if (!response.Success)
            {
                Logger.Error(response.Message, response.Exception);
                throw new UserFriendlyException($"Cannot get new third party gift list from {thirdPartyCategory.Vendor.Type}.");
            }
            var gifts = response.Gifts;
            var roundType = GetRoundingType();

            var listResult = new List<ThirdPartyGiftInfo>();

            // Get old selected Gift code & new selected Gift code
            var oldSelectedCodes = oldGifts.Select(t => t.ThirdPartyGiftCode).ToList();
            var newSelectedCodes = gifts.Select(t => t.Code).ToList();

            // Find unselect gifts, create new gifts and update info gifts 
            var needRemoveGiftCodes = oldSelectedCodes.Except(newSelectedCodes).ToList();
            var needCreateGiftCodes = newSelectedCodes.Except(oldSelectedCodes).ToList();
            var needUpdateGiftCodes = oldSelectedCodes.Intersect(newSelectedCodes).ToList();

            // Set unselect gift
            var needRemoveGifts = oldGifts.Where(t => needRemoveGiftCodes.Contains(t.ThirdPartyGiftCode)).ToList();
            foreach (var item in needRemoveGifts)
            {
                var obj = ObjectMapper.Map<ThirdPartyGiftInfo>(item);
                obj.Code = item.ThirdPartyGiftCode;

                if (thirdPartyCategory?.Vendor?.CoinPriceExchangeRate != 0)
                {
                    obj.SellInPrice = obj.VendorPrice / (decimal)thirdPartyCategory?.Vendor?.CoinPriceExchangeRate;
                }
                else
                {
                    obj.SellInPrice = 0;
                }

                obj.SellOutPrice = RoundExtensions.RoundByType(obj.SellInPrice * (1 + obj.Margin / 100), roundType);
                obj.Selected = false;
                obj.Note = $"Gift was removed or disabled.";
                obj.IsChanged = !string.IsNullOrEmpty(obj.Note);

                listResult.Add(obj);
            }
            //needUnselectedGifts.ForEach(t => t.Selected = false);

            //var publishGift = new List<GiftInfor>();

            //Update gifts info
            var needUpdateGifts = gifts.Where(t => needUpdateGiftCodes.Contains(t.Code)).ToList();
            foreach (var gift in needUpdateGifts)
            {
                var oldGift = oldGifts.FirstOrDefault(t => t.ThirdPartyGiftCode == gift.Code);
                gift.Selected = oldGift?.Selected ?? false;
                gift.Margin = (decimal)thirdPartyCategory?.Vendor?.DefaultMargin;

                gift.Note = oldGift?.Note3rdPartyChange;
                gift.IsChanged = !string.IsNullOrEmpty(gift.Note);

                // Apply manual margin that user inputs
                if (oldGift != null)
                {
                    if (!oldGift.IsDefaultMargin)
                    {
                        gift.IsDefaultMargin = oldGift.IsDefaultMargin;
                        gift.Margin = oldGift.Margin;
                    }
                    // check vendor price
                    var noteChange = string.Empty;
                    if (oldGift.VendorPrice != gift.VendorPrice && oldGift.Selected)
                    {
                        gift.VendorPrice = oldGift.VendorPrice;
                        noteChange += $" | Vendor Price was changed";
                        if (thirdPartyCategory?.Vendor?.CoinPriceExchangeRate != 0)
                        {
                            gift.SellInPrice = gift.VendorPrice / (decimal)thirdPartyCategory?.Vendor?.CoinPriceExchangeRate;
                        }
                        else
                        {
                            gift.SellInPrice = 0;
                        }
                    }
                    if (thirdPartyCategory?.Vendor?.Type == "Urbox")
                    {
                        gift.EffectiveFrom = oldGift?.EffectiveFrom ?? DateTime.UtcNow;
                        gift.EffectiveTo = oldGift?.EffectiveTo ?? DateTime.UtcNow.AddYears(10);
                    }
                    else if (thirdPartyCategory?.Vendor?.Type == "Dealtoday")
                    {
                        if (oldGift.EffectiveFrom != gift.EffectiveFrom && oldGift.Selected)
                        {
                            noteChange += $" | Vendor Effective from was changed";
                        }
                        if (oldGift.EffectiveTo != gift.EffectiveTo && oldGift.Selected)
                        {
                            noteChange += gift.Note + $" | Vendor Effective to was changed";
                        }
                    }

                    if (!string.IsNullOrWhiteSpace(noteChange))
                    {
                        gift.Note = noteChange.TrimStart().TrimStart('|');
                        gift.IsChanged = !string.IsNullOrEmpty(gift.Note);
                        gift.Selected = false;
                    }
                }

                gift.SellOutPrice = RoundExtensions.RoundByType(gift.SellInPrice * (1 + gift.Margin / 100), roundType);
                //gift.LastModificationTime = oldGift?.LastModificationTime ?? DateTime.UtcNow;

                listResult.Add(gift);
            }

            //New gifts info
            var needCreateGifts = gifts.Where(t => needCreateGiftCodes.Contains(t.Code)).ToList();
            foreach (var gift in needCreateGifts)
            {
                gift.Margin = (decimal)thirdPartyCategory?.Vendor?.DefaultMargin;
                gift.SellOutPrice = RoundExtensions.RoundByType(gift.SellInPrice * (1 + gift.Margin / 100), roundType);
                listResult.Add(gift);
            }

            //foreach (var gift in gifts)
            //{
            //    var oldGift = oldGifts.FirstOrDefault(t => t.ThirdPartyGiftCode == gift.Code);
            //    gift.Selected = oldGift?.Selected ?? false;
            //    gift.Margin = (decimal)thirdPartyCategory.Vendor.DefaultMargin;

            //    // Apply manual margin that user inputs
            //    if (oldGift != null && !oldGift.IsDefaultMargin)
            //    {
            //        gift.IsDefaultMargin = oldGift.IsDefaultMargin;
            //        gift.Margin = oldGift.Margin;
            //    }

            //    gift.EffectiveFrom = oldGift?.EffectiveFrom;
            //    gift.EffectiveTo = oldGift?.EffectiveTo;

            //    gift.SellOutPrice = RoundExtensions.RoundByType(gift.SellInPrice * (1 + gift.Margin / 100), roundType);
            //}

            return new GetThirdPartyGiftsOutput
            {
                ThirdPartyGifts = listResult
            };
        }

        public async Task<GetThirdPartyGiftsOutput> ViewThirdPartyGiftsByCategory(GetThirdPartyGiftsByCategoryInput input)
        {
            if (input.Categories.Count() == 0)
                return new GetThirdPartyGiftsOutput();

            var listResult = new List<ThirdPartyGiftInfo>();
            var i = 0;
            var categoryDistinct = input.Categories.Distinct().ToList();
            do
            {
                var category = categoryDistinct[i];
                var vendor3rd = _thirdPartyGiftVendorRepository.GetAll()
                .Where(t => t.Id == category.VendorId).FirstOrDefault();

                if (vendor3rd == null)
                    continue;

                var giftCategory = await _giftCategoryRepository.GetAll().Include(e => e.TargetAudience).ThenInclude(e => e.TargetAudienceDetail)
                .FirstOrDefaultAsync(e => e.Code == category.ThirdPartyCategoryCode);
                if (giftCategory == null)
                    continue;

                var oldGifts = _giftInforRepository.GetAll().Where(e => e.Is3rdPartyGift
                                    && e.VendorId.Value == vendor3rd.Id
                                    && e.ThirdPartyCategoryCode == category.ThirdPartyCategoryCode).ToList();

                var adapter = ThirdPartyVendorAdapterFactory.GetAdapter(vendor3rd.Type, ObjectMapper.Map<ThirdPartyVendorApiInfo>(vendor3rd), Logger, _unitOfWorkManager, _clientInfoProvider, _auditLogRepository, _httpContextAccessor);
                var response = await adapter.GetGiftCategory(new ThirdPartyGiftCategoryRequest
                {
                    CategoryId = category.ThirdPartyCategoryId
                }, vendor3rd.TenantId);

                if (!response.Success)
                {
                    Logger.Error(response.Message, response.Exception);
                    throw new UserFriendlyException($"Cannot get new third party gift list from {vendor3rd.Type}.");
                }
                var gifts = response.Gifts;
                var roundType = GetRoundingType();

                // Get old selected Gift code & new selected Gift code
                var oldSelectedCodes = oldGifts.Select(t => t.ThirdPartyGiftCode).ToList();
                var newSelectedCodes = gifts.Select(t => t.Code).ToList();

                // Find unselect gifts, create new gifts and update info gifts 
                var needRemoveGiftCodes = oldSelectedCodes.Except(newSelectedCodes).ToList();
                var needCreateGiftCodes = newSelectedCodes.Except(oldSelectedCodes).ToList();
                var needUpdateGiftCodes = oldSelectedCodes.Intersect(newSelectedCodes).ToList();

                // Set unselect gift
                var needRemoveGifts = oldGifts.Where(t => needRemoveGiftCodes.Contains(t.ThirdPartyGiftCode)).ToList();
                foreach (var item in needRemoveGifts)
                {
                    var obj = ObjectMapper.Map<ThirdPartyGiftInfo>(item);
                    obj.Code = item.ThirdPartyGiftCode;

                    if (vendor3rd?.CoinPriceExchangeRate != 0)
                    {
                        obj.SellInPrice = obj.VendorPrice / (decimal)vendor3rd?.CoinPriceExchangeRate;
                    }
                    else
                    {
                        obj.SellInPrice = 0;
                    }

                    obj.SellOutPrice = RoundExtensions.RoundByType(obj.SellInPrice * (1 + obj.Margin / 100), roundType);
                    var listVendorUsingImport = ThirdPartyGiftVendorUsingImportGift.VendorUsing;
                    var vendorHasUsingImport = listVendorUsingImport.Find(x => x == vendor3rd.Type);
                    if (vendorHasUsingImport == null)
                    {
                        obj.Selected = false;
                        obj.Note = $"Gift was removed or disabled.";
                        obj.IsChanged = !string.IsNullOrEmpty(obj.Note);
                    }
                    obj.VendorId = vendor3rd.Id;
                    obj.ThirdPartyGiftCategoryId = category.ThirdPartyCategoryId.ToString();
                    obj.VendorName = vendor3rd.VendorName;
                    obj.ThirdPartyCategoryId = giftCategory.Id;
                    listResult.Add(obj);
                }

                //Update gifts info
                var needUpdateGifts = gifts.Where(t => needUpdateGiftCodes.Contains(t.Code)).ToList();
                foreach (var gift in needUpdateGifts)
                {
                    var oldGift = oldGifts.FirstOrDefault(t => t.ThirdPartyGiftCode == gift.Code);
                    gift.Selected = oldGift?.Selected ?? false;
                    gift.Margin = (decimal)vendor3rd?.DefaultMargin;

                    gift.Note = oldGift?.Note3rdPartyChange;
                    gift.IsChanged = !string.IsNullOrEmpty(gift.Note);

                    // Apply manual margin that user inputs
                    if (oldGift != null)
                    {
                        if (!oldGift.IsDefaultMargin)
                        {
                            gift.IsDefaultMargin = oldGift.IsDefaultMargin;
                            gift.Margin = oldGift.Margin;
                        }
                        // check vendor price
                        var noteChange = string.Empty;
                        if (oldGift.VendorPrice != gift.VendorPrice && oldGift.Selected)
                        {
                            gift.VendorPrice = oldGift.VendorPrice;
                            noteChange += $" | Vendor Price was changed";
                            if (vendor3rd?.CoinPriceExchangeRate != 0)
                            {
                                gift.SellInPrice = gift.VendorPrice / (decimal)vendor3rd?.CoinPriceExchangeRate;
                            }
                            else
                            {
                                gift.SellInPrice = 0;
                            }
                        }
                        if (vendor3rd.Type == "Urbox")
                        {
                            gift.EffectiveFrom = oldGift?.EffectiveFrom ?? DateTime.UtcNow;
                            gift.EffectiveTo = oldGift?.EffectiveTo ?? DateTime.UtcNow.AddYears(10);
                        }
                        else if (vendor3rd.Type == "Dealtoday")
                        {
                            if (oldGift.EffectiveFrom != gift.EffectiveFrom && oldGift.Selected)
                            {
                                noteChange += $" | Vendor Effective from was changed";
                            }
                            if (oldGift.EffectiveTo != gift.EffectiveTo && oldGift.Selected)
                            {
                                noteChange += gift.Note + $" | Vendor Effective to was changed";
                            }
                        }

                        if (!string.IsNullOrWhiteSpace(noteChange))
                        {
                            gift.Note = noteChange.TrimStart().TrimStart('|');
                            gift.IsChanged = !string.IsNullOrEmpty(gift.Note);
                            gift.Selected = false;
                        }
                    }

                    gift.SellOutPrice = RoundExtensions.RoundByType(gift.SellInPrice * (1 + gift.Margin / 100), roundType);
                    //gift.LastModificationTime = oldGift?.LastModificationTime ?? DateTime.UtcNow;
                    gift.VendorId = vendor3rd.Id;
                    gift.ThirdPartyGiftCategoryId = category.ThirdPartyCategoryId.ToString();
                    gift.ThirdPartyCategoryId = giftCategory.Id;
                    gift.VendorName = vendor3rd.VendorName;
                    listResult.Add(gift);
                }

                //New gifts info
                var needCreateGifts = gifts.Where(t => needCreateGiftCodes.Contains(t.Code)).ToList();
                foreach (var gift in needCreateGifts)
                {
                    gift.Margin = (decimal)vendor3rd?.DefaultMargin;
                    gift.SellOutPrice = RoundExtensions.RoundByType(gift.SellInPrice * (1 + gift.Margin / 100), roundType);
                    gift.VendorId = vendor3rd.Id;
                    gift.ThirdPartyGiftCategoryId = category.ThirdPartyCategoryId.ToString();
                    gift.VendorName = vendor3rd.VendorName;
                    gift.ThirdPartyCategoryId = giftCategory.Id;
                    listResult.Add(gift);
                }
                i++;
            } while (i < categoryDistinct.Count());

            return new GetThirdPartyGiftsOutput
            {
                ThirdPartyGifts = listResult
            };
        }

        /// <summary>
        /// Gets the type of the rounding.
        /// </summary>
        /// <returns></returns>
        private string GetRoundingType()
        {
            var roundingType = PointSavingTypeConsts.Round;
            var currentNumberRoundingSetting = _systemSettingRepository.FirstOrDefault(t => t.Code == Constants.SystemSettingCode);
            if (currentNumberRoundingSetting != null)
            {
                roundingType = currentNumberRoundingSetting.Type ?? PointSavingTypeConsts.Round;
            }
            return SavingTypeConst.SavingTypeMappingDictionary[roundingType];
        }

        private void Update3rdPartyGiftStatus(string category3rdCode, string status)
        {
            var gifts3rd = _giftInforRepository.GetAll().Where(x => x.ThirdPartyCategoryCode == category3rdCode)
                // .WhereIf(status == "A", x => x.Disable == true)
                .WhereIf(status.Equals(StatusConst.InActive), x => x.Status.Equals(StatusConst.Active)).ToList();
            if (gifts3rd != null && gifts3rd.Any())
            {
                gifts3rd.ForEach(x =>
                {
                    x.Status = status;
                    x.Disable = !x.Disable;
                });
            }
        }

        #endregion Third Party Gift Category

        private async Task<string> MapInternalCategoryName(CustomizeGiftInfosDto giftInfoDto)
        {
            var internalCategoryName = string.Empty;
            var fullGiftCategoryCode = string.Empty;
            if (giftInfoDto != null)
            {

                if (string.IsNullOrEmpty(giftInfoDto.FullGiftCategoryCode))
                {
                    return internalCategoryName;
                }
                else
                {
                    fullGiftCategoryCode = giftInfoDto.FullGiftCategoryCode.Split(",").Last().Trim();
                }
                internalCategoryName = (await _giftCategoryRepository.FirstOrDefaultAsync(x => x.Code == fullGiftCategoryCode))?.Name;
            }
            return internalCategoryName;
        }
    }
}
